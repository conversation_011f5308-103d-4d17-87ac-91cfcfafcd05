import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  User,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentUserController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<User> {
    return this.incidentRepository.reportedBy(id);
  }
}
