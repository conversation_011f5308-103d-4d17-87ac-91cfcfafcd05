import {Entity, hasMany, model, property} from '@loopback/repository';
import {LocationThree} from './location-three.model';

@model()
export class LocationTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  locationOneId?: string;

  @hasMany(() => LocationThree)
  locationThrees: LocationThree[];

  constructor(data?: Partial<LocationTwo>) {
    super(data);
  }
}

export interface LocationTwoRelations {
  // describe navigational properties here
}

export type LocationTwoWithRelations = LocationTwo & LocationTwoRelations;
