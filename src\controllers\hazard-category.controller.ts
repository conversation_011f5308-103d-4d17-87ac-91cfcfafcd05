import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HazardCategory} from '../models';
import {HazardCategoryRepository} from '../repositories';

export class HazardCategoryController {
  constructor(
    @repository(HazardCategoryRepository)
    public hazardCategoryRepository : HazardCategoryRepository,
  ) {}

  @post('/hazard-categories')
  @response(200, {
    description: 'HazardCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(HazardCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardCategory, {
            title: 'NewHazardCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    hazardCategory: Omit<HazardCategory, 'id'>,
  ): Promise<HazardCategory> {
    return this.hazardCategoryRepository.create(hazardCategory);
  }

  @get('/hazard-categories/count')
  @response(200, {
    description: 'HazardCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HazardCategory) where?: Where<HazardCategory>,
  ): Promise<Count> {
    return this.hazardCategoryRepository.count(where);
  }

  @get('/hazard-categories')
  @response(200, {
    description: 'Array of HazardCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HazardCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HazardCategory) filter?: Filter<HazardCategory>,
  ): Promise<HazardCategory[]> {
    return this.hazardCategoryRepository.find(filter);
  }

  @patch('/hazard-categories')
  @response(200, {
    description: 'HazardCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardCategory, {partial: true}),
        },
      },
    })
    hazardCategory: HazardCategory,
    @param.where(HazardCategory) where?: Where<HazardCategory>,
  ): Promise<Count> {
    return this.hazardCategoryRepository.updateAll(hazardCategory, where);
  }

  @get('/hazard-categories/{id}')
  @response(200, {
    description: 'HazardCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HazardCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HazardCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<HazardCategory>
  ): Promise<HazardCategory> {
    return this.hazardCategoryRepository.findById(id, filter);
  }

  @patch('/hazard-categories/{id}')
  @response(204, {
    description: 'HazardCategory PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HazardCategory, {partial: true}),
        },
      },
    })
    hazardCategory: HazardCategory,
  ): Promise<void> {
    await this.hazardCategoryRepository.updateById(id, hazardCategory);
  }

  @put('/hazard-categories/{id}')
  @response(204, {
    description: 'HazardCategory PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() hazardCategory: HazardCategory,
  ): Promise<void> {
    await this.hazardCategoryRepository.replaceById(id, hazardCategory);
  }

  @del('/hazard-categories/{id}')
  @response(204, {
    description: 'HazardCategory DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.hazardCategoryRepository.deleteById(id);
  }
}
