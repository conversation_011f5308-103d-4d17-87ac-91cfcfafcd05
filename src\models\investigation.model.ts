import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {Action} from './action.model';
import {Incident} from './incident.model';
import {InvestigationControl} from './investigation-control.model';
import {InvestigationRecommendation} from './investigation-recommendation.model';
import {User} from './user.model';

@model()
export class Investigation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  status?: "Not Started" | "Under Investigation" | "Pending Verification" | "Completed" | "Recalled" | "Returned";

  @property({
    type: 'boolean',
  })
  isArchive?: boolean;

  @property({
    type: 'string',
  })
  investigationTeamMembers?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'object',
    itemType: InvestigationControl,
    jsonSchema: getJsonSchema(InvestigationControl)
  })
  controls?: InvestigationControl;

  @property({
    type: 'string',
  })
  conclusionRemarks?: string;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Action)

  })
  totalActions?: Action[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Action)

  })
  completedActions?: Action[];

  @property({
    type: 'string',
  })
  investigationApproverRemarks?: string;

  @belongsTo(() => Incident)
  incidentId: string;

  @belongsTo(() => User)
  investigatorId: string;

  @hasMany(() => InvestigationRecommendation)
  investigationRecommendations: InvestigationRecommendation[];

  @belongsTo(() => User)
  investigationApproverId: string;

  constructor(data?: Partial<Investigation>) {
    super(data);
  }
}

export interface InvestigationRelations {
  // describe navigational properties here
}

export type InvestigationWithRelations = Investigation & InvestigationRelations;
