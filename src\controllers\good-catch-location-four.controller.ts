import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  LocationFour,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchLocationFourController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<LocationFour> {
    return this.goodCatchRepository.locationFour(id);
  }
}
