import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  DeepPartial,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, NearTermControlMeasures} from '../models';
import {ActionRepository, IncidentRepository, NearTermControlMeasuresRepository, ServiceRepository} from '../repositories';

const SERVICE_NAME = 'INCINV';

export class IncidentWithNearTermControlMeasures {

  nearTermControlMeasures: Omit<NearTermControlMeasures, 'id'>[];
}

@authenticate('cognito-jwt')
export class NearTermControlMeasuresController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(NearTermControlMeasuresRepository)
    public nearTermControlMeasuresRepository: NearTermControlMeasuresRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @post('/submit-incident/{actionId}')
  @response(200, {
    description: 'Action created for Near Term Control Measures',
    content: {'application/json': {schema: getModelSchemaRef(NearTermControlMeasures)}},
  })
  async createOttWithTask(
    @inject(SecurityBindings.USER) currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string
  ): Promise<NearTermControlMeasures[]> {
    // Step 1: Fetch the actionData using actionId from actionRepository
    const actionData = await this.actionRepository.findById(actionId);

    if (!actionData) {
      throw new HttpErrors.NotFound(`Action with id ${actionId} not found`);
    }

    // Step 2: Extract applicationId from the actionData, which is the incidentId
    const incidentId = actionData.applicationId;

    // Step 3: Use the incidentRepository to fetch the incident using incidentId
    const incident = await this.incidentRepository.findById(incidentId, {
      include: [{relation: 'nearTermControlMeasures'}] // Include related NTCMs
    });

    // Step 4: If no NearTermControlMeasures found, skip action creation and update status
    if (!incident || !incident.nearTermControlMeasures || incident.nearTermControlMeasures.length === 0) {
      // No NTCMs found, just update the action status to 'Completed' and the incident to 'Reviewed'
      await this.actionRepository.updateById(actionId, {status: 'Completed'});
      await this.incidentRepository.updateById(incidentId, {status: 'Reviewed'});

      // Return empty array since no control measures were processed
      return [];
    }

    // Step 5: Fetch the service by SERVICE_NAME
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}});
    if (!service) {
      throw new HttpErrors.NotFound('Service not found');
    }

    // Step 6: Initialize array to hold NearTermControlMeasures
    const ntcm: NearTermControlMeasures[] = [];

    // Step 7: Iterate over the nearTermControlMeasures from the incident and create actions for each
    await Promise.all(
      incident.nearTermControlMeasures.map(async (nearTermControlMeasure) => {
        // Create an Action object for each control measure
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'perform_task',
          actionToBeTaken: nearTermControlMeasure.controlMeasure,
          description: nearTermControlMeasure.controlMeasure,
          maskId: nearTermControlMeasure.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'NTCM-TASK',
          applicationId: nearTermControlMeasure.incidentId,
          dueDate: nearTermControlMeasure.dueDate,
          objectId: nearTermControlMeasure.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [nearTermControlMeasure.responsibleId],
          submitURL: '/ntcm-task-submit',
          status: 'Initiated',
          serviceId: service.id,
        };

        // Insert the action into actionRepository
        await this.actionRepository.create(actions);

        // Add the control measure to the ntcm array
        ntcm.push(nearTermControlMeasure);
      })
    );

    // Step 8: Update the original action's status to 'Completed'
    await this.actionRepository.updateById(actionId, {status: 'Completed'});

    // Update the incident status to 'Reviewed'
    await this.incidentRepository.updateById(incidentId, {status: 'Reviewed'});

    // Step 9: Return the fetched control measures
    return ntcm;
  }


  @patch('/ntcm-task-submit/{actionId}')
  @response(204, {
    description: 'NTCM PATCH success',
  })
  async submitByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              actionRequestData: getModelSchemaRef(Action, {partial: true}),
              nearTermControlMeasures: getModelSchemaRef(NearTermControlMeasures, {partial: true}),
            },

          },
        },
      },
    })
    requestData: {actionRequestData?: Partial<Action>; nearTermControlMeasures?: Partial<NearTermControlMeasures>}

  ): Promise<void> {
    const {actionRequestData, nearTermControlMeasures} = requestData;

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const ntcmData = await this.nearTermControlMeasuresRepository.findById(actionData.objectId)
    if (!ntcmData) {
      throw new Error('No NTCM Data Found')
    }

    let reviewerComments = '';
    let status: DeepPartial<'Yet to Start' | 'Planning' | 'In Progress: On Track' | 'At Risk' | 'On Hold' | 'Under Review' | 'Testing / QA' | 'Ready for Deployment' | 'Completed' | 'Archived' | 'Returned' | undefined> = 'In Progress: On Track'
    switch (actionData.actionType) {
      case 'perform_task': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_task',
          actionToBeTaken: 'Review Task Completion',
          description: ntcmData.controlMeasure,
          maskId: ntcmData.maskId,
          trackId: actionData.trackId,
          sequence: '1',
          prefix: 'NTCM-TASK',
          actionTaken: actionRequestData?.actionTaken,
          uploads: actionRequestData?.uploads,
          applicationId: ntcmData.incidentId,
          objectId: ntcmData.id,
          dueDate: ntcmData.dueDate,
          submittedById: currentUserProfile[securityId],
          assignedToId: [nearTermControlMeasures?.reviewerId ?? ''],
          submitURL: '/ntcm-task-submit',
          status: 'Initiated',
          serviceId: service.id
        };
        status = "Under Review"

        // Insert into actionRepository
        await this.actionRepository.updateById(actionId, {actionTaken: actionRequestData?.actionTaken, uploads: actionRequestData?.uploads})
        await this.nearTermControlMeasuresRepository.updateById(actionData.objectId, {reviewerId: ntcmData.reviewerId});
        await this.actionRepository.create(actions);
        break;
      }


      case 'reperform_task':
        {
          const actions: Partial<Action> = {
            application: SERVICE_NAME,
            actionType: 'verify_task',
            actionToBeTaken: 'Review Task Completion',
            description: ntcmData.controlMeasure,
            maskId: ntcmData.maskId,
            trackId: actionData.trackId,
            sequence: actionData.sequence,
            prefix: 'NTCM-TASK',
            applicationId: ntcmData.incidentId,
            objectId: ntcmData.id,
            submittedById: currentUserProfile[securityId],
            actionTaken: actionRequestData?.actionTaken,
            uploads: actionRequestData?.uploads,
            assignedToId: [nearTermControlMeasures?.reviewerId ?? ''],
            submitURL: '/ntcm-task-submit',
            dueDate: ntcmData.dueDate,
            status: 'Initiated',
            serviceId: service.id
          };
          status = "Under Review"

          // Insert into actionRepository
          await this.actionRepository.updateById(actionId, {actionTaken: actionRequestData?.actionTaken, uploads: actionRequestData?.uploads})
          await this.nearTermControlMeasuresRepository.updateById(actionData.objectId, {reviewerId: nearTermControlMeasures?.reviewerId});
          await this.actionRepository.create(actions);
          break;
        }
      case 'verify_task':
        {
          reviewerComments = nearTermControlMeasures?.reviewerComments ?? ''
          if (nearTermControlMeasures?.status !== 'Returned' && nearTermControlMeasures?.status !== 'Completed') {
            throw new Error('Method Type Status not allowed')
          }

          switch (nearTermControlMeasures.status) {
            case 'Returned':
              {
                const actions: Partial<Action> =
                {
                  application: SERVICE_NAME,
                  actionType: 'reperform_task',
                  actionToBeTaken: 'Revise & Update Status',
                  description: ntcmData.controlMeasure,
                  maskId: ntcmData.maskId,
                  trackId: actionData.trackId, // Generate unique id
                  sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
                  prefix: 'NTCM-TASK',
                  applicationId: ntcmData.incidentId,
                  remarks: reviewerComments,
                  comments: reviewerComments,
                  actionTaken: actionData.actionTaken,
                  uploads: actionData.uploads,
                  dueDate: ntcmData.dueDate,
                  objectId: ntcmData.id,
                  submittedById: currentUserProfile[securityId],
                  assignedToId: [ntcmData.responsibleId],
                  submitURL: '/ntcm-task-submit',
                  status: 'Initiated',
                  serviceId: service.id
                };

                status = "Returned"
                // Insert into actionRepository
                await this.actionRepository.updateById(actionId, {comments: reviewerComments, remarks: reviewerComments})
                await this.actionRepository.create(actions);
                break;
              }
            case 'Completed':
              {
                await this.actionRepository.updateById(actionId, {comments: reviewerComments, remarks: reviewerComments})
                status = "Completed"
                break;
              }

            default: throw new Error('Method Type Status not allowed')
          }

          break;
        }

      default: throw new Error('Action type not allowed')
    }
    await this.actionRepository.updateById(actionId, {status: 'Completed'})
    //optimize the reviewer comments concept. it should be avaible only when reviewer submits it
    await this.nearTermControlMeasuresRepository.updateById(actionData.objectId, {reviewerComments: reviewerComments, status: status});
  }

  @post('/near-term-control-measures')
  @response(200, {
    description: 'NearTermControlMeasures model instance',
    content: {'application/json': {schema: getModelSchemaRef(NearTermControlMeasures)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NearTermControlMeasures, {
            title: 'NewNearTermControlMeasures',
            exclude: ['id'],
          }),
        },
      },
    })
    nearTermControlMeasures: Omit<NearTermControlMeasures, 'id'>,
  ): Promise<NearTermControlMeasures> {
    try {
      if (!nearTermControlMeasures.incidentId) {
        throw new Error('Incident ID is required.');
      }

      const incidentData = await this.incidentRepository.findById(nearTermControlMeasures.incidentId);
      const count = await this.nearTermControlMeasuresRepository.count({incidentId: nearTermControlMeasures.incidentId});

      nearTermControlMeasures.maskId = `${incidentData.maskId}-NTCM-${count.count + 1}`;
      nearTermControlMeasures.userId = currentUserProfile[securityId];
      nearTermControlMeasures.status = 'Yet to Start';

      return await this.nearTermControlMeasuresRepository.create(nearTermControlMeasures);
    } catch (error) {
      // Handle different types of errors appropriately
      console.error(error);
      throw new HttpErrors.BadRequest(error.message);
    }
  }

  @get('/near-term-control-measures/count')
  @response(200, {
    description: 'NearTermControlMeasures model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NearTermControlMeasures) where?: Where<NearTermControlMeasures>,
  ): Promise<Count> {
    return this.nearTermControlMeasuresRepository.count(where);
  }

  @get('/near-term-control-measures')
  @response(200, {
    description: 'Array of NearTermControlMeasures model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NearTermControlMeasures, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NearTermControlMeasures) filter?: Filter<NearTermControlMeasures>,
  ): Promise<NearTermControlMeasures[]> {
    return this.nearTermControlMeasuresRepository.find(filter);
  }

  @patch('/near-term-control-measures')
  @response(200, {
    description: 'NearTermControlMeasures PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NearTermControlMeasures, {partial: true}),
        },
      },
    })
    nearTermControlMeasures: NearTermControlMeasures,
    @param.where(NearTermControlMeasures) where?: Where<NearTermControlMeasures>,
  ): Promise<Count> {
    return this.nearTermControlMeasuresRepository.updateAll(nearTermControlMeasures, where);
  }

  @get('/near-term-control-measures/{id}')
  @response(200, {
    description: 'NearTermControlMeasures model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NearTermControlMeasures, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(NearTermControlMeasures, {exclude: 'where'}) filter?: FilterExcludingWhere<NearTermControlMeasures>
  ): Promise<NearTermControlMeasures> {
    return this.nearTermControlMeasuresRepository.findById(id, filter);
  }

  @patch('/near-term-control-measures/{id}')
  @response(204, {
    description: 'NearTermControlMeasures PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NearTermControlMeasures, {partial: true}),
        },
      },
    })
    nearTermControlMeasures: NearTermControlMeasures,
  ): Promise<void> {
    await this.nearTermControlMeasuresRepository.updateById(id, nearTermControlMeasures);
  }

  @put('/near-term-control-measures/{id}')
  @response(204, {
    description: 'NearTermControlMeasures PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() nearTermControlMeasures: NearTermControlMeasures,
  ): Promise<void> {
    await this.nearTermControlMeasuresRepository.replaceById(id, nearTermControlMeasures);
  }

  @del('/near-term-control-measures/{id}')
  @response(204, {
    description: 'NearTermControlMeasures DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.nearTermControlMeasuresRepository.deleteById(id);
  }
}
