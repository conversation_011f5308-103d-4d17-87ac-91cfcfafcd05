import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  RiskAssessment,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportRiskAssessmentController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/risk-assessment', {
    responses: {
      '200': {
        description: 'RiskAssessment belonging to PermitReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(RiskAssessment),
          },
        },
      },
    },
  })
  async getRiskAssessment(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<RiskAssessment> {
    return this.permitReportRepository.riskAssessment(id);
  }
}
