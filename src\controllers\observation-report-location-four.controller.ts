import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  LocationFour,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportLocationFourController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<LocationFour> {
    return this.observationReportRepository.locationFour(id);
  }
}
