import {belongsTo, Entity, model, property} from '@loopback/repository';
import {User} from './user.model';

@model()
export class RaTeamMember extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  signature?: string;

  @property({
    type: 'date',
  })
  signatureDate?: string;

  @property({
    type: 'string',
  })
  status?: 'Initiated' | 'Signed';

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => User)
  userId: string;

  @property({
    type: 'string',
  })
  riskAssessmentId?: string;

  constructor(data?: Partial<RaTeamMember>) {
    super(data);
  }
}

export interface RaTeamMemberRelations {
  // describe navigational properties here
}

export type RaTeamMemberWithRelations = RaTeamMember & RaTeamMemberRelations;
