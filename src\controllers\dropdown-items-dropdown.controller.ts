import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DropdownItems,
  Dropdown,
} from '../models';
import {DropdownItemsRepository} from '../repositories';

export class DropdownItemsDropdownController {
  constructor(
    @repository(DropdownItemsRepository)
    public dropdownItemsRepository: DropdownItemsRepository,
  ) { }

  @get('/dropdown-items/{id}/dropdown', {
    responses: {
      '200': {
        description: 'Dropdown belonging to DropdownItems',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Dropdown),
          },
        },
      },
    },
  })
  async getDropdown(
    @param.path.string('id') id: typeof DropdownItems.prototype.id,
  ): Promise<Dropdown> {
    return this.dropdownItemsRepository.dropdown(id);
  }
}
