import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {Dropdown, DropdownRelations, DropdownItems, Service} from '../models';
import {DropdownItemsRepository} from './dropdown-items.repository';
import {ServiceRepository} from './service.repository';

export class DropdownRepository extends DefaultCrudRepository<
  Dropdown,
  typeof Dropdown.prototype.id,
  DropdownRelations
> {

  public readonly dropdownItems: HasManyRepositoryFactory<DropdownItems, typeof Dropdown.prototype.id>;

  public readonly service: BelongsToAccessor<Service, typeof Dropdown.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('DropdownItemsRepository') protected dropdownItemsRepositoryGetter: Getter<DropdownItemsRepository>, @repository.getter('ServiceRepository') protected serviceRepositoryGetter: Getter<ServiceRepository>,
  ) {
    super(Dropdown, dataSource);
    this.service = this.createBelongsToAccessorFor('service', serviceRepositoryGetter,);
    this.registerInclusionResolver('service', this.service.inclusionResolver);
    this.dropdownItems = this.createHasManyRepositoryFactoryFor('dropdownItems', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('dropdownItems', this.dropdownItems.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
