import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ToolboxSignStatus} from '../models';
import {ToolboxSignStatusRepository} from '../repositories';

export class ToolboxSignStatusController {
  constructor(
    @repository(ToolboxSignStatusRepository)
    public toolboxSignStatusRepository : ToolboxSignStatusRepository,
  ) {}

  @post('/toolbox-sign-statuses')
  @response(200, {
    description: 'ToolboxSignStatus model instance',
    content: {'application/json': {schema: getModelSchemaRef(ToolboxSignStatus)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxSignStatus, {
            title: 'NewToolboxSignStatus',
            exclude: ['id'],
          }),
        },
      },
    })
    toolboxSignStatus: Omit<ToolboxSignStatus, 'id'>,
  ): Promise<ToolboxSignStatus> {
    return this.toolboxSignStatusRepository.create(toolboxSignStatus);
  }

  @get('/toolbox-sign-statuses/count')
  @response(200, {
    description: 'ToolboxSignStatus model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ToolboxSignStatus) where?: Where<ToolboxSignStatus>,
  ): Promise<Count> {
    return this.toolboxSignStatusRepository.count(where);
  }

  @get('/toolbox-sign-statuses')
  @response(200, {
    description: 'Array of ToolboxSignStatus model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ToolboxSignStatus, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ToolboxSignStatus) filter?: Filter<ToolboxSignStatus>,
  ): Promise<ToolboxSignStatus[]> {
    return this.toolboxSignStatusRepository.find(filter);
  }

  @patch('/toolbox-sign-statuses')
  @response(200, {
    description: 'ToolboxSignStatus PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxSignStatus, {partial: true}),
        },
      },
    })
    toolboxSignStatus: ToolboxSignStatus,
    @param.where(ToolboxSignStatus) where?: Where<ToolboxSignStatus>,
  ): Promise<Count> {
    return this.toolboxSignStatusRepository.updateAll(toolboxSignStatus, where);
  }

  @get('/toolbox-sign-statuses/{id}')
  @response(200, {
    description: 'ToolboxSignStatus model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ToolboxSignStatus, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ToolboxSignStatus, {exclude: 'where'}) filter?: FilterExcludingWhere<ToolboxSignStatus>
  ): Promise<ToolboxSignStatus> {
    return this.toolboxSignStatusRepository.findById(id, filter);
  }

  @patch('/toolbox-sign-statuses/{id}')
  @response(204, {
    description: 'ToolboxSignStatus PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxSignStatus, {partial: true}),
        },
      },
    })
    toolboxSignStatus: ToolboxSignStatus,
  ): Promise<void> {
    await this.toolboxSignStatusRepository.updateById(id, toolboxSignStatus);
  }

  @put('/toolbox-sign-statuses/{id}')
  @response(204, {
    description: 'ToolboxSignStatus PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() toolboxSignStatus: ToolboxSignStatus,
  ): Promise<void> {
    await this.toolboxSignStatusRepository.replaceById(id, toolboxSignStatus);
  }

  @del('/toolbox-sign-statuses/{id}')
  @response(204, {
    description: 'ToolboxSignStatus DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.toolboxSignStatusRepository.deleteById(id);
  }
}
