import {Entity, model, property} from '@loopback/repository';

@model()
export class Hazard extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  image?: string;

  @property({
    type: 'string',
  })
  hazardsCategoryId?: string;


  constructor(data?: Partial<Hazard>) {
    super(data);
  }
}

export interface HazardRelations {
  // describe navigational properties here
}

export type HazardWithRelations = Hazard & HazardRelations;
