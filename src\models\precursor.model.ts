import {Entity, model, property} from '@loopback/repository';

@model()
export class Precursor extends Entity {


  @property({
    type: 'string',
    required: false,
    description: 'What work was being performed when the incident occurred?',
  })
  workPerformed?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were any specific tasks or procedures being followed at the time?',
  })
  tasksProcedures?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were all team members aware of their roles and responsibilities at the time of the incident?',
  })
  rolesAwareness?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were any shortcuts or deviations from standard procedures taken?',
  })
  shortcutsDeviations?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were there any recent changes in workload, procedures or staffing?',
  })
  recentChanges?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Was there a specific deadline or pressure to complete the task?',
  })
  deadlinePressure?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were there any distractions or interruptions during the work being performed?',
  })
  distractionsInterruptions?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Was there anything unusual or different about the operating conditions on the day of the incident?',
  })
  unusualConditions?: string;

  @property({
    type: 'string',
    required: false,
    description: 'What equipment or tools were in use during the incident?',
  })
  equipmentUsed?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were there any signs of malfunction or damage to equipment before the incident?',
  })
  equipmentMalfunction?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Had any maintenance, repair or inspections been performed recently on the equipment involved?',
  })
  recentMaintenance?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Had there been any similar near-miss events or safety concerns reported previously?',
  })
  similarIncidents?: string;

  @property({
    type: 'string',
  })
  status?: "Draft" | "Completed";

  constructor(data?: Partial<Precursor>) {
    super(data);
  }
}

export interface PrecursorRelations {
  // describe navigational properties here
}

export type PrecursorWithRelations = Precursor & PrecursorRelations;
