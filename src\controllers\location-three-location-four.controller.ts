import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {
  LocationFour,
  LocationThree,
} from '../models';
import {LocationThreeRepository, LocationTwoRepository, UserLocationRoleRepository, UserRepository} from '../repositories';

@authenticate('cognito-jwt')
export class LocationThreeLocationFourController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(UserLocationRoleRepository) protected userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
  ) { }

  @get('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many LocationFour',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationFour)},
          },
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationFour>,
  ): Promise<LocationFour[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});

    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }

    // Check for 'tier1-all' access
    const hasTier1AllAccess = await this.userLocationRoleRepository.findOne({
      where: {userId: user.id, locationOneId: 'tier1-all'}
    });

    if (hasTier1AllAccess) {
      return this.locationThreeRepository.locationFours(id).find(filter);
    }

    // Fetch the associated locationTwoId for the provided locationThreeId
    const locationThree = await this.locationThreeRepository.findById(id);
    const associatedLocationTwoId = locationThree.locationTwoId;

    // Check if the user has 'tier3-all' access for the associated locationTwoId
    const hasTier3AllAccessForAssociatedLocationTwo = await this.userLocationRoleRepository.findOne({
      where: {userId: user.id, locationTwoId: associatedLocationTwoId, locationThreeId: 'tier3-all'}
    });

    if (hasTier3AllAccessForAssociatedLocationTwo) {
      return this.locationThreeRepository.locationFours(id).find(filter);
    }

    // Fetch the associated locationOneId for the associated locationTwoId
    const locationTwo = await this.locationTwoRepository.findById(associatedLocationTwoId);
    const associatedLocationOneId = locationTwo.locationOneId;

    // Check if the user has 'tier2-all' access for the associated locationOneId
    const hasTier2AllAccessForAssociatedLocationOne = await this.userLocationRoleRepository.findOne({
      where: {userId: user.id, locationOneId: associatedLocationOneId, locationTwoId: 'tier2-all'}
    });

    if (hasTier2AllAccessForAssociatedLocationOne) {
      return this.locationThreeRepository.locationFours(id).find(filter);
    }

    const userLocationRoles = await this.userLocationRoleRepository.find({where: {userId: user.id, locationThreeId: id}});
    const locationFourIds = userLocationRoles.map((userLocationRole) => userLocationRole.locationFourId);
    const uniqueLocationFourIds = Array.from(new Set(locationFourIds));

    if (uniqueLocationFourIds.includes('tier4-all')) {
      return this.locationThreeRepository.locationFours(id).find(filter);
    } else {
      return this.locationThreeRepository.locationFours(id).find({
        where: {id: {inq: uniqueLocationFourIds}},
      });
    }
  }

  @post('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationFour)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {
            title: 'NewLocationFourInLocationThree',
            exclude: ['id'],
            optional: ['locationThreeId']
          }),
        },
      },
    }) locationFour: Omit<LocationFour, 'id'>,
  ): Promise<LocationFour> {
    return this.locationThreeRepository.locationFours(id).create(locationFour);
  }

  @patch('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree.LocationFour PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {partial: true}),
        },
      },
    })
    locationFour: Partial<LocationFour>,
    @param.query.object('where', getWhereSchemaFor(LocationFour)) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationThreeRepository.locationFours(id).patch(locationFour, where);
  }

  @del('/location-threes/{id}/location-fours', {
    responses: {
      '200': {
        description: 'LocationThree.LocationFour DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationFour)) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationThreeRepository.locationFours(id).delete(where);
  }
}
