import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  DeepPartial,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, Ott, OttTask} from '../models';
import {ActionRepository, OttRepository, OttTaskRepository, ServiceRepository} from '../repositories';

const SERVICE_NAME = 'OTT';

export class OttWithTasks {
  ott: Omit<Ott, 'id'>;
  tasks: Omit<OttTask, 'id' | 'ottId'>[];
}

@authenticate('cognito-jwt')
export class OttController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(OttRepository)
    public ottRepository: OttRepository,
    @repository(OttTaskRepository)
    public ottTaskRepository: OttTaskRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
  ) { }

  @post('/ott-with-tasks')
  @response(200, {
    description: 'Ott model instance',
    content: {'application/json': {schema: getModelSchemaRef(Ott)}},
  })
  async createOttWithTask(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              ott: getModelSchemaRef(Ott, {
                title: 'newOtt',
                exclude: ['id'],
              }),
              tasks: {
                type: 'array',
                items: getModelSchemaRef(OttTask, {
                  title: 'newTasks',
                  exclude: ['id', 'ottId'],
                }),
              },
            },
            required: ['ott', 'tasks'],
          },
        },
      },
    })
    ottWithTasks: OttWithTasks,
  ): Promise<Ott> {

    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.ottRepository.count();

    ottWithTasks.ott.maskId = `${SERVICE_NAME}-${year}${month}${day}-${count.count + 1}`;
    ottWithTasks.ott.creatorId = currentUserProfile[securityId];
    ottWithTasks.ott.status = 'Yet to Start'
    const ott = await this.ottRepository.create(ottWithTasks.ott);
    for (const task of ottWithTasks.tasks) {
      await this.ottTaskRepository.create(
        {
          ...task,
          ottId: ott.id
        }
      );
    }

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }



    const actions: Partial<Action> =
    {
      application: SERVICE_NAME,
      actionType: 'perform_task',
      actionToBeTaken: 'Update Status',
      description: ott.taskDescription,
      maskId: ott.maskId,
      trackId: uuidv4(), // Generate unique id
      sequence: '1',
      prefix: 'OTT-TASK',
      applicationId: ott.id,
      dueDate: ott.dueDate,
      objectId: ott.id,
      submittedById: currentUserProfile[securityId],
      assignedToId: [ott.assigneeId],
      submitURL: '/ott-task-submit',
      status: 'Initiated',
      serviceId: service.id,

    };


    // Insert into actionRepository
    await this.actionRepository.create(actions);

    return ott;
  }

  @patch('/ott-task-submit/{actionId}')
  @response(204, {
    description: 'Ott PATCH success',
  })
  async submitByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ott, {partial: true}),
        },
      },
    })
    ott: Ott,
  ): Promise<void> {
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const ottData = await this.ottRepository.findById(actionData.objectId)
    if (!ottData) {
      throw new Error('No OTT Data Found')
    }
    let reviewerComments = '';
    let status: DeepPartial<'Yet to Start' | 'Planning' | 'In Progress: On Track' | 'At Risk' | 'On Hold' | 'Under Review' | 'Testing / QA' | 'Ready for Deployment' | 'Completed' | 'Archived' | 'Returned' | undefined> = 'In Progress: On Track'
    switch (actionData.actionType) {
      case 'perform_task': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_task',
          actionToBeTaken: 'Review Task Completion',
          description: ottData.taskDescription,
          maskId: ottData.maskId,
          trackId: actionData.trackId,
          sequence: '1',
          prefix: 'OTT-TASK',
          applicationId: ottData.id,
          objectId: ottData.id,
          dueDate: ottData.dueDate,
          submittedById: currentUserProfile[securityId],
          assignedToId: [ott.reviewerId],
          submitURL: '/ott-task-submit',
          status: 'Initiated',
          serviceId: service.id
        };
        status = "Under Review"

        // Insert into actionRepository
        await this.ottRepository.updateById(actionData.applicationId, {reviewerId: ott.reviewerId});
        await this.actionRepository.create(actions);
        break;
      }


      case 'reperform_task':
        {
          const actions: Partial<Action> = {
            application: SERVICE_NAME,
            actionType: 'verify_task',
            actionToBeTaken: 'Review Task Completion',
            description: ottData.taskDescription,
            maskId: ottData.maskId,
            trackId: actionData.trackId,
            sequence: actionData.sequence,
            prefix: 'OTT-TASK',
            applicationId: ottData.id,
            objectId: ottData.id,
            submittedById: currentUserProfile[securityId],
            assignedToId: [ott.reviewerId],
            submitURL: '/ott-task-submit',
            dueDate: ottData.dueDate,
            status: 'Initiated',
            serviceId: service.id
          };
          status = "Under Review"

          // Insert into actionRepository
          await this.ottRepository.updateById(actionData.applicationId, {reviewerId: ott.reviewerId});
          await this.actionRepository.create(actions);
          break;
        }
      case 'verify_task':
        {
          reviewerComments = ott.reviewerComments ?? ''
          if (ott.status !== 'Returned' && ott.status !== 'Completed') {
            throw new Error('Method Type Status not allowed')
          }

          switch (ott.status) {
            case 'Returned':
              {
                const actions: Partial<Action> =
                {
                  application: SERVICE_NAME,
                  actionType: 'reperform_task',
                  actionToBeTaken: 'Revise & Update Status',
                  description: ottData.taskDescription,
                  maskId: ottData.maskId,
                  trackId: uuidv4(), // Generate unique id
                  sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
                  prefix: 'OTT-TASK',
                  applicationId: ottData.id,
                  dueDate: ottData.dueDate,
                  objectId: ottData.id,
                  submittedById: currentUserProfile[securityId],
                  assignedToId: [ottData.assigneeId],
                  submitURL: '/ott-task-submit',
                  status: 'Initiated',
                  serviceId: service.id
                };

                status = "Returned"
                // Insert into actionRepository
                await this.actionRepository.create(actions);
                break;
              }
            case 'Completed':
              {
                status = "Completed"
                await this.ottRepository.updateById(actionData.applicationId, {isArchive: true});
                break;
              }

            default: throw new Error('Method Type Status not allowed')
          }

          break;
        }

      default: throw new Error('Action type not allowed')
    }
    await this.actionRepository.updateById(actionId, {status: 'Completed'})
    //optimize the reviewer comments concept. it should be avaible only when reviewer submits it
    await this.ottRepository.updateById(actionData.applicationId, {reviewerComments: reviewerComments, status: status});
  }

  @post('/otts')
  @response(200, {
    description: 'Ott model instance',
    content: {'application/json': {schema: getModelSchemaRef(Ott)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ott, {
            title: 'NewOtt',
            exclude: ['id'],
          }),
        },
      },
    })
    ott: Omit<Ott, 'id'>,
  ): Promise<Ott> {
    return this.ottRepository.create(ott);
  }

  @get('/otts/count')
  @response(200, {
    description: 'Ott model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Ott) where?: Where<Ott>,
  ): Promise<Count> {
    return this.ottRepository.count(where);
  }

  @get('/otts')
  @response(200, {
    description: 'Array of Ott model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Ott, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Ott) filter?: Filter<Ott>,
  ): Promise<Ott[]> {
    return this.ottRepository.find(filter);
  }

  @patch('/otts')
  @response(200, {
    description: 'Ott PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ott, {partial: true}),
        },
      },
    })
    ott: Ott,
    @param.where(Ott) where?: Where<Ott>,
  ): Promise<Count> {
    return this.ottRepository.updateAll(ott, where);
  }

  @get('/otts/{id}')
  @response(200, {
    description: 'Ott model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Ott, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Ott, {exclude: 'where'}) filter?: FilterExcludingWhere<Ott>
  ): Promise<Ott> {
    return this.ottRepository.findById(id, filter);
  }

  @patch('/otts/{id}')
  @response(204, {
    description: 'Ott PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ott, {partial: true}),
        },
      },
    })
    ott: Ott,
  ): Promise<void> {
    await this.ottRepository.updateById(id, ott);
  }

  @put('/otts/{id}')
  @response(204, {
    description: 'Ott PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ott: Ott,
  ): Promise<void> {
    await this.ottRepository.replaceById(id, ott);
  }

  @del('/otts/{id}')
  @response(204, {
    description: 'Ott DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ottRepository.deleteById(id);
  }
}
