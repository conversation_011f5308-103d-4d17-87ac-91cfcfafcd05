import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RiskUpdate,
  RiskAssessment,
} from '../models';
import {RiskUpdateRepository} from '../repositories';

export class RiskUpdateRiskAssessmentController {
  constructor(
    @repository(RiskUpdateRepository)
    public riskUpdateRepository: RiskUpdateRepository,
  ) { }

  @get('/risk-updates/{id}/risk-assessment', {
    responses: {
      '200': {
        description: 'RiskAssessment belonging to RiskUpdate',
        content: {
          'application/json': {
            schema: getModelSchemaRef(RiskAssessment),
          },
        },
      },
    },
  })
  async getRiskAssessment(
    @param.path.string('id') id: typeof RiskUpdate.prototype.id,
  ): Promise<RiskAssessment> {
    return this.riskUpdateRepository.riskAssessment(id);
  }
}
