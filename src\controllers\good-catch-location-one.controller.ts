import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  LocationOne,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchLocationOneController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<LocationOne> {
    return this.goodCatchRepository.locationOne(id);
  }
}
