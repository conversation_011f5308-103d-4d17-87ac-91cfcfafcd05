import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GoodCatch,
  Action,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchActionController {
  constructor(
    @repository(GoodCatchRepository) protected goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of GoodCatch has many Action',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Action)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.goodCatchRepository.actions(id).find(filter);
  }

  @post('/good-catches/{id}/actions', {
    responses: {
      '200': {
        description: 'GoodCatch model instance',
        content: {'application/json': {schema: getModelSchemaRef(Action)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInGoodCatch',
            exclude: ['id'],
            optional: ['goodCatchId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.goodCatchRepository.actions(id).create(action);
  }

  @patch('/good-catches/{id}/actions', {
    responses: {
      '200': {
        description: 'GoodCatch.Action PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.goodCatchRepository.actions(id).patch(action, where);
  }

  @del('/good-catches/{id}/actions', {
    responses: {
      '200': {
        description: 'GoodCatch.Action DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.goodCatchRepository.actions(id).delete(where);
  }
}
