import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  Checklist,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionChecklistController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/checklist', {
    responses: {
      '200': {
        description: 'Checklist belonging to Inspection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Checklist),
          },
        },
      },
    },
  })
  async getChecklist(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<Checklist> {
    return this.inspectionRepository.checklist(id);
  }
}
