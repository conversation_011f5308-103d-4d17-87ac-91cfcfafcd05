import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  LocationFour,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportLocationFourController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to PermitReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<LocationFour> {
    return this.permitReportRepository.locationFour(id);
  }
}
