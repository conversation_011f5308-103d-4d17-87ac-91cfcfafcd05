import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  LocationOne,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkLocationOneController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<LocationOne> {
    return this.toolboxTalkRepository.locationOne(id);
  }
}
