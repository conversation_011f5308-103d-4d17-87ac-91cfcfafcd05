import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {Action} from './action.model';
import {Checklist} from './checklist.model';
import {LocationFive} from './location-five.model';
import {LocationFour} from './location-four.model';
import {LocationOne} from './location-one.model';
import {LocationSix} from './location-six.model';
import {LocationThree} from './location-three.model';
import {LocationTwo} from './location-two.model';
import {User} from './user.model';

@model()
export class Inspection extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  value?: any;

  @property({
    type: 'any',
  })
  inspectionActions?: any;

  @property({
    type: 'date',
  })
  scheduledDate?: string;

  @property({
    type: 'string',
  })
  inspectionCategory?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'date',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  status?: 'Scheduled' | 'In Progress' | 'Completed' | 'Archived' | 'Completed without Actions' | 'Completed with Actions' | 'Archived without Completion';

  @property({
    type: 'string',
  })
  checklistVersion?: string;

  @property({
    type: 'date',
  })
  actualCompletionDate?: string;

  @belongsTo(() => User)
  inspectorId: string;

  @belongsTo(() => User)
  assignedById: string;

  @hasMany(() => Action, {keyTo: 'applicationId'})
  actions: Action[];

  @belongsTo(() => Checklist)
  checklistId: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Action)

  })
  totalActions?: Action[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Action)

  })
  completedActions?: Action[];

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  constructor(data?: Partial<Inspection>) {
    super(data);
  }
}

export interface InspectionRelations {
  // describe navigational properties here
}

export type InspectionWithRelations = Inspection & InspectionRelations;
