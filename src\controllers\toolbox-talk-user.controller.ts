import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  User,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkUserController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<User> {
    return this.toolboxTalkRepository.conductedBy(id);
  }
}
