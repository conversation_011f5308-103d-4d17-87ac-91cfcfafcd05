import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  LocationFour,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentLocationFourController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<LocationFour> {
    return this.incidentRepository.locationFour(id);
  }
}
