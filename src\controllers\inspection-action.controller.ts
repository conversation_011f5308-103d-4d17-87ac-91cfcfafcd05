import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Inspection,
  Action,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionActionController {
  constructor(
    @repository(InspectionRepository) protected inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of Inspection has many Action',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Action)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.inspectionRepository.actions(id).find(filter);
  }

  @post('/inspections/{id}/actions', {
    responses: {
      '200': {
        description: 'Inspection model instance',
        content: {'application/json': {schema: getModelSchemaRef(Action)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Inspection.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInInspection',
            exclude: ['id'],
            optional: ['applicationId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.inspectionRepository.actions(id).create(action);
  }

  @patch('/inspections/{id}/actions', {
    responses: {
      '200': {
        description: 'Inspection.Action PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.inspectionRepository.actions(id).patch(action, where);
  }

  @del('/inspections/{id}/actions', {
    responses: {
      '200': {
        description: 'Inspection.Action DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.inspectionRepository.actions(id).delete(where);
  }
}
