import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  DeepPartial,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {
  Action,
  Investigation,
  InvestigationRecommendation
} from '../models';
import {ActionRepository, IncidentRepository, InvestigationRecommendationRepository, InvestigationRepository, ServiceRepository} from '../repositories';

const SERVICE_NAME = 'INCINV';

@authenticate('cognito-jwt')
export class InvestigationInvestigationRecommendationController {
  constructor(
    @repository(InvestigationRepository) protected investigationRepository: InvestigationRepository,
    @repository(ActionRepository) protected actionRepository: ActionRepository,
    @repository(ServiceRepository) protected serviceRepository: ServiceRepository,
    @repository(InvestigationRecommendationRepository) protected investigationRecommendationRepository: InvestigationRecommendationRepository,
    @repository(IncidentRepository) protected incidentRepository: IncidentRepository,
  ) { }

  @get('/investigations/{id}/investigation-recommendations', {
    responses: {
      '200': {
        description: 'Array of Investigation has many InvestigationRecommendation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(InvestigationRecommendation)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<InvestigationRecommendation>,
  ): Promise<InvestigationRecommendation[]> {
    return this.investigationRepository.investigationRecommendations(id).find(filter);
  }

  @post('/investigations/{id}/investigation-recommendations', {
    responses: {
      '200': {
        description: 'Investigation model instance',
        content: {'application/json': {schema: getModelSchemaRef(InvestigationRecommendation)}},
      },
    },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: typeof Investigation.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecommendation, {
            title: 'NewInvestigationRecommendationInInvestigation',
            exclude: ['id'],
            optional: ['investigationId']
          }),
        },
      },
    }) investigationRecommendation: Omit<InvestigationRecommendation, 'id'>,
  ): Promise<InvestigationRecommendation> {
    const investigationData = await this.investigationRepository.findById(id);
    const incidentData = await this.incidentRepository.findById(investigationData.incidentId)
    const count = await this.investigationRecommendationRepository.count({investigationId: id});
    investigationRecommendation.maskId = `${incidentData.maskId}-PICM-${count.count + 1}`;
    investigationRecommendation.submittedById = currentUserProfile[securityId];
    investigationRecommendation.status = 'Yet to Start';


    return this.investigationRepository.investigationRecommendations(id).create(investigationRecommendation);
  }

  @patch('/investigations/{id}/investigation-recommendations', {
    responses: {
      '200': {
        description: 'Investigation.InvestigationRecommendation PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecommendation, {partial: true}),
        },
      },
    })
    investigationRecommendation: Partial<InvestigationRecommendation>,
    @param.query.object('where', getWhereSchemaFor(InvestigationRecommendation)) where?: Where<InvestigationRecommendation>,
  ): Promise<Count> {
    return this.investigationRepository.investigationRecommendations(id).patch(investigationRecommendation, where);
  }

  @del('/investigations/{id}/investigation-recommendations', {
    responses: {
      '200': {
        description: 'Investigation.InvestigationRecommendation DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(InvestigationRecommendation)) where?: Where<InvestigationRecommendation>,
  ): Promise<Count> {
    return this.investigationRepository.investigationRecommendations(id).delete(where);
  }

  @post('/approve-investigation/{actionId}')
  @response(200, {
    description: 'Action created for Near Term Control Measures',
    content: {'application/json': {schema: getModelSchemaRef(InvestigationRecommendation)}},
  })
  async createRecommendationWithTask(
    @inject(SecurityBindings.USER) currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              approverComments: {type: 'string'},
              recommendations: {
                type: 'array',
                items: getModelSchemaRef(InvestigationRecommendation),
              },
            },
            required: ['recommendations'],
          },
        },
      },
    })
    requestData: {approverComments?: string; recommendations: InvestigationRecommendation[]}
  ): Promise<InvestigationRecommendation[]> {
    // Step 1: Fetch the actionData using actionId from actionRepository
    const {approverComments, recommendations} = requestData;
    const actionData = await this.actionRepository.findById(actionId);

    if (!actionData) {
      throw new HttpErrors.NotFound(`Action with id ${actionId} not found`);
    }

    // Step 2: Extract applicationId from the actionData, which is the incidentId
    const incidentId = actionData.applicationId;
    const investigationId = actionData.objectId;

    // Step 3: Use the incidentRepository to fetch the incident using incidentId
    const investigation = await this.investigationRepository.findById(investigationId, {
      include: [{relation: 'investigationRecommendations'}] // Include related NTCMs
    });

    // Step 4: If no NearTermControlMeasures found, skip action creation and update status
    if (!investigation || !recommendations || recommendations.length === 0) {
      // No NTCMs found, just update the action status to 'Completed' and the incident to 'Reviewed'
      await this.investigationRepository.updateById(investigationId, {status: 'Completed', investigationApproverRemarks: approverComments ?? ''});
      await this.incidentRepository.updateById(incidentId, {status: 'Investigation Completed'})
      await this.actionRepository.updateById(actionId, {status: 'Completed'})

      // Return empty array since no control measures were processed
      return [];
    }

    // Step 5: Fetch the service by SERVICE_NAME
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}});
    if (!service) {
      throw new HttpErrors.NotFound('Service not found');
    }

    // Step 6: Initialize array to hold NearTermControlMeasures
    const picm: InvestigationRecommendation[] = [];

    // Step 7: Iterate over the nearTermControlMeasures from the incident and create actions for each
    await Promise.all(
      recommendations.map(async (investigationRecommendation) => {
        // Create an Action object for each control measure
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'perform_task',
          actionToBeTaken: investigationRecommendation.recommendedActions,
          description: investigationRecommendation.rationaleForRecommendation,
          maskId: investigationRecommendation.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'PICM-TASK',
          applicationId: investigationId,
          dueDate: investigationRecommendation.dueDate,
          objectId: investigationRecommendation.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [investigationRecommendation.assignedToId],
          submitURL: '/picm-task-submit',
          status: 'Initiated',
          serviceId: service.id,
        };

        // Insert the action into actionRepository
        await this.actionRepository.create(actions);
        await this.investigationRecommendationRepository.updateById(investigationRecommendation.id, investigationRecommendation);
        // Add the control measure to the ntcm array
        picm.push(investigationRecommendation);
      })
    );

    // Step 8: Update the original action's status to 'Completed'
    await this.investigationRepository.updateById(investigationId, {status: 'Completed', investigationApproverRemarks: approverComments ?? ''});
    await this.incidentRepository.updateById(incidentId, {status: 'Investigation Completed'})
    await this.actionRepository.updateById(actionId, {status: 'Completed'})
    // Step 9: Return the fetched control measures
    return picm;
  }


  @patch('/picm-task-submit/{actionId}')
  @response(204, {
    description: 'PICM PATCH success',
  })
  async submitByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              actionRequestData: getModelSchemaRef(Action, {partial: true}),
              investigationRecommendation: getModelSchemaRef(InvestigationRecommendation, {partial: true}),
            },

          },
        },
      },
    })
    requestData: {actionRequestData?: Partial<Action>; investigationRecommendation?: Partial<InvestigationRecommendation>}

  ): Promise<void> {
    const {actionRequestData, investigationRecommendation} = requestData;
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const picmData = await this.investigationRecommendationRepository.findById(actionData.objectId)
    if (!picmData) {
      throw new Error('No PICM Data Found')
    }

    let reviewerComments = '';
    let status: DeepPartial<'Yet to Start' | 'Planning' | 'In Progress: On Track' | 'At Risk' | 'On Hold' | 'Under Review' | 'Testing / QA' | 'Ready for Deployment' | 'Completed' | 'Archived' | 'Returned' | undefined> = 'In Progress: On Track'
    switch (actionData.actionType) {
      case 'perform_task': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_task',
          actionToBeTaken: 'Review Task Completion',
          description: picmData.rationaleForRecommendation,
          maskId: picmData.maskId,
          trackId: actionData.trackId,
          sequence: '1',
          prefix: 'PICM-TASK',
          actionTaken: actionRequestData?.actionTaken,
          uploads: actionRequestData?.uploads,
          applicationId: actionData.applicationId,
          objectId: picmData.id,
          dueDate: picmData.dueDate,
          submittedById: currentUserProfile[securityId],
          assignedToId: [investigationRecommendation?.reviewerId ?? ''],
          submitURL: '/picm-task-submit',
          status: 'Initiated',
          serviceId: service.id
        };
        status = "Under Review"

        // Insert into actionRepository
        await this.actionRepository.updateById(actionId, {actionTaken: actionRequestData?.actionTaken, uploads: actionRequestData?.uploads})
        await this.investigationRecommendationRepository.updateById(actionData.objectId, {reviewerId: investigationRecommendation?.reviewerId});
        await this.actionRepository.create(actions);
        break;
      }


      case 'reperform_task':
        {
          const actions: Partial<Action> = {
            application: SERVICE_NAME,
            actionType: 'verify_task',
            actionToBeTaken: 'Review Task Completion',
            description: picmData.rationaleForRecommendation,
            maskId: picmData.maskId,
            trackId: actionData.trackId,
            sequence: actionData.sequence,
            prefix: 'PICM-TASK',
            applicationId: actionData.applicationId,
            objectId: picmData.id,
            submittedById: currentUserProfile[securityId],
            actionTaken: actionRequestData?.actionTaken,
            uploads: actionRequestData?.uploads,
            assignedToId: [investigationRecommendation?.reviewerId ?? ''],
            submitURL: '/picm-task-submit',
            dueDate: picmData.dueDate,
            status: 'Initiated',
            serviceId: service.id
          };
          status = "Under Review"

          // Insert into actionRepository
          await this.actionRepository.updateById(actionId, {actionTaken: actionRequestData?.actionTaken, uploads: actionRequestData?.uploads})
          await this.investigationRecommendationRepository.updateById(actionData.objectId, {reviewerId: investigationRecommendation?.reviewerId});
          await this.actionRepository.create(actions);
          break;
        }
      case 'verify_task':
        {
          reviewerComments = investigationRecommendation?.reviewerComments ?? ''
          if (investigationRecommendation?.status !== 'Returned' && investigationRecommendation?.status !== 'Completed') {
            throw new Error('Method Type Status not allowed')
          }

          switch (investigationRecommendation.status) {
            case 'Returned':
              {
                const actions: Partial<Action> =
                {
                  application: SERVICE_NAME,
                  actionType: 'reperform_task',
                  actionToBeTaken: 'Revise & Update Status',
                  description: picmData.rationaleForRecommendation,
                  maskId: picmData.maskId,
                  trackId: actionData.trackId, // Generate unique id
                  sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
                  prefix: 'PICM-TASK',
                  remarks: reviewerComments,
                  comments: reviewerComments,
                  actionTaken: actionData.actionTaken,
                  uploads: actionData.uploads,
                  applicationId: actionData.applicationId,
                  dueDate: picmData.dueDate,
                  objectId: picmData.id,
                  submittedById: currentUserProfile[securityId],
                  assignedToId: [picmData.assignedToId],
                  submitURL: '/picm-task-submit',
                  status: 'Initiated',
                  serviceId: service.id
                };

                status = "Returned"
                // Insert into actionRepository
                await this.actionRepository.updateById(actionId, {comments: reviewerComments, remarks: reviewerComments})
                await this.actionRepository.create(actions);
                break;
              }
            case 'Completed':
              {
                await this.actionRepository.updateById(actionId, {comments: reviewerComments, remarks: reviewerComments})
                status = "Completed"
                break;
              }

            default: throw new Error('Method Type Status not allowed')
          }

          break;
        }

      default: throw new Error('Action type not allowed')
    }
    await this.actionRepository.updateById(actionId, {status: 'Completed'})
    //optimize the reviewer comments concept. it should be avaible only when reviewer submits it
    await this.investigationRecommendationRepository.updateById(actionData.objectId, {reviewerComments: reviewerComments, status: status});
  }
}
