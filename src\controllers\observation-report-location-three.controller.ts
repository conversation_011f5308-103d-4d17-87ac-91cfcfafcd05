import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  LocationThree,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportLocationThreeController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<LocationThree> {
    return this.observationReportRepository.locationThree(id);
  }
}
