import {belongsTo, Entity, model, property} from '@loopback/repository';
import {Incident} from './incident.model';
import {User} from './user.model';

@model()
export class NearTermControlMeasures extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  controlMeasure?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  reviewerComments?: string;

  @property({
    type: 'string',
  })
  approverComments?: string;

  @property({
    type: 'string',
  })
  status?: 'Yet to Start' | 'In Progress: On Track' | 'Under Review' | 'Completed' | 'Archived' | 'Returned';

  @property({
    type: 'date',
  })
  dueDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => User)
  userId: string;

  @belongsTo(() => Incident)
  incidentId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => User)
  approverId: string;

  @belongsTo(() => User)
  responsibleId: string;

  constructor(data?: Partial<NearTermControlMeasures>) {
    super(data);
  }
}

export interface NearTermControlMeasuresRelations {
  // describe navigational properties here
}

export type NearTermControlMeasuresWithRelations = NearTermControlMeasures & NearTermControlMeasuresRelations;
