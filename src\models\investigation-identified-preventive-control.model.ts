import {Entity, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {JobFactor} from './job-factor.model';
import {RelatedOrganizationalFactor} from './related-organizational-factor.model';

@model()
export class InvestigationIdentifiedPreventiveControl extends Entity {

  @property({
    type: 'string',
  })
  immediateCause?: string;

  @property({
    type: 'string',
  })
  immediateCauseDescription?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  controlStatement?: string;

  @property({
    type: 'boolean',
  })
  isControlImplemented?: boolean;

  @property({
    type: 'boolean',
  })
  isEffective?: boolean;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(JobFactor)

  })
  jobFactors?: JobFactor[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(RelatedOrganizationalFactor)

  })
  relatedOrganizationalFactors?: RelatedOrganizationalFactor[];

  constructor(data?: Partial<InvestigationIdentifiedPreventiveControl>) {
    super(data);
  }
}

export interface InvestigationIdentifiedPreventiveControlRelations {
  // describe navigational properties here
}

export type InvestigationIdentifiedPreventiveControlWithRelations = InvestigationIdentifiedPreventiveControl & InvestigationIdentifiedPreventiveControlRelations;
