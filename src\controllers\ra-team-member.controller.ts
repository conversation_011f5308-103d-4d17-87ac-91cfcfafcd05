import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import moment from 'moment';
import {RaTeamMember} from '../models';
import {ActionRepository, RaTeamMemberRepository, RiskAssessmentRepository} from '../repositories';

export class RaTeamMemberController {
  constructor(
    @repository(RaTeamMemberRepository)
    public raTeamMemberRepository: RaTeamMemberRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @post('/ra-team-members')
  @response(200, {
    description: 'RaTeamMember model instance',
    content: {'application/json': {schema: getModelSchemaRef(RaTeamMember)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaTeamMember, {
            title: 'NewRaTeamMember',
            exclude: ['id'],
          }),
        },
      },
    })
    raTeamMember: Omit<RaTeamMember, 'id'>,
  ): Promise<RaTeamMember> {
    return this.raTeamMemberRepository.create(raTeamMember);
  }

  @get('/ra-team-members/count')
  @response(200, {
    description: 'RaTeamMember model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(RaTeamMember) where?: Where<RaTeamMember>,
  ): Promise<Count> {
    return this.raTeamMemberRepository.count(where);
  }

  @get('/ra-team-members')
  @response(200, {
    description: 'Array of RaTeamMember model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RaTeamMember, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(RaTeamMember) filter?: Filter<RaTeamMember>,
  ): Promise<RaTeamMember[]> {
    return this.raTeamMemberRepository.find(filter);
  }

  @patch('/ra-team-members')
  @response(200, {
    description: 'RaTeamMember PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaTeamMember, {partial: true}),
        },
      },
    })
    raTeamMember: RaTeamMember,
    @param.where(RaTeamMember) where?: Where<RaTeamMember>,
  ): Promise<Count> {
    return this.raTeamMemberRepository.updateAll(raTeamMember, where);
  }

  @get('/ra-team-members/{id}')
  @response(200, {
    description: 'RaTeamMember model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RaTeamMember, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(RaTeamMember, {exclude: 'where'}) filter?: FilterExcludingWhere<RaTeamMember>
  ): Promise<RaTeamMember> {
    return this.raTeamMemberRepository.findById(id, filter);
  }




  @patch('/ra-team-member-submit-signature/{actionId}')
  @response(204, {
    description: 'RaTeamMember PATCH success',
  })
  async updateSignatureById(

    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaTeamMember, {partial: true}),
        },
      },
    })
    raTeamMember: RaTeamMember,
  ): Promise<void> {
    // Set signatureDate and status
    raTeamMember.signatureDate = new Date().toISOString();
    raTeamMember.status = 'Signed';

    // Fetch the action object by ID
    const action = await this.actionRepository.findById(actionId);
    if (!action) {
      throw new HttpErrors.NotFound(`Action with id ${actionId} not found`);
    }

    const id = action.objectId;

    // Count total and signed team members
    const [countTotal, countSigned] = await Promise.all([
      this.raTeamMemberRepository.count({riskAssessmentId: action.applicationId}),
      this.raTeamMemberRepository.count({riskAssessmentId: action.applicationId, status: 'Signed'}),
    ]);

    // Update the action status
    await this.actionRepository.updateById(actionId, {status: 'Completed'});
    await this.raTeamMemberRepository.updateById(id, raTeamMember);
    console.log(countTotal, countSigned, 'sign')
    // Check if all team members have signed
    if (countTotal.count === countSigned.count + 1) {
      const nextReviewDate = moment().add(1, 'year').utc().format('YYYY-MM-DDTHH:mm:ssZ');
      const publishedDate = moment().utc().format('YYYY-MM-DDTHH:mm:ssZ');
      await this.riskAssessmentRepository.updateById(action.applicationId, {status: 'Published', nextReviewDate: nextReviewDate, publishedDate: publishedDate});
    }
  }

  @patch('/ra-team-members/{id}')
  @response(204, {
    description: 'RaTeamMember PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaTeamMember, {partial: true}),
        },
      },
    })
    raTeamMember: RaTeamMember,
  ): Promise<void> {
    await this.raTeamMemberRepository.updateById(id, raTeamMember);
  }

  @put('/ra-team-members/{id}')
  @response(204, {
    description: 'RaTeamMember PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() raTeamMember: RaTeamMember,
  ): Promise<void> {
    await this.raTeamMemberRepository.replaceById(id, raTeamMember);
  }

  @del('/ra-team-members/{id}')
  @response(204, {
    description: 'RaTeamMember DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.raTeamMemberRepository.deleteById(id);
  }
}
