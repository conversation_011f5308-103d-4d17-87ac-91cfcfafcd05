import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {DropdownItems} from './dropdown-items.model';
import {Service} from './service.model';

@model()
export class Dropdown extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  maskName?: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  levels?: string[];

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  apiUrl?: string;

  @property({
    type: 'string',
  })
  routeUrl?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  type?: "General" | "Application";

  @hasMany(() => DropdownItems)
  dropdownItems: DropdownItems[];

  @belongsTo(() => Service)
  serviceId: string;

  constructor(data?: Partial<Dropdown>) {
    super(data);
  }
}

export interface DropdownRelations {
  // describe navigational properties here
}

export type DropdownWithRelations = Dropdown & DropdownRelations;
