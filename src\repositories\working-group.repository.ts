import {inject} from '@loopback/core';
import {DefaultCrudRepository, juggler} from '@loopback/repository';
import {WorkingGroup, WorkingGroupRelations} from '../models';

export class WorkingGroupRepository extends DefaultCrudRepository<
  WorkingGroup,
  typeof WorkingGroup.prototype.id,
  WorkingGroupRelations
> {
  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
  ) {
    super(WorkingGroup, dataSource);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}


