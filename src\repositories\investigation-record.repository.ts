import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {Incident, InvestigationRecord, InvestigationRecordRelations} from '../models';
import {IncidentRepository} from './incident.repository';

export class InvestigationRecordRepository extends DefaultCrudRepository<
  InvestigationRecord,
  typeof InvestigationRecord.prototype.id,
  InvestigationRecordRelations
> {

  public readonly incident: BelongsToAccessor<Incident, typeof InvestigationRecord.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('IncidentRepository') protected incidentRepositoryGetter: Getter<IncidentRepository>,
  ) {
    super(InvestigationRecord, dataSource);
    this.incident = this.createBelongsToAccessorFor('incident', incidentRepositoryGetter,);
    this.registerInclusionResolver('incident', this.incident.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
