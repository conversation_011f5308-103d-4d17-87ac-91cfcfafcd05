import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {HazardCategory, HazardCategoryRelations, Hazards} from '../models';
import {HazardsRepository} from './hazards.repository';

export class HazardCategoryRepository extends DefaultCrudRepository<
  HazardCategory,
  typeof HazardCategory.prototype.id,
  HazardCategoryRelations
> {

  public readonly hazards: HasManyRepositoryFactory<Hazards, typeof HazardCategory.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('HazardsRepository') protected hazardsRepositoryGetter: Getter<HazardsRepository>,
  ) {
    super(HazardCategory, dataSource);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });

    this.hazards = this.createHasManyRepositoryFactoryFor('hazards', hazardsRepositoryGetter,);
    this.registerInclusionResolver('hazards', this.hazards.inclusionResolver);
  }
}
