import {inject} from '@loopback/core';
import {
  param,
  patch,
  post,
  requestBody,
  response
} from '@loopback/rest';
import {
  SecurityBindings,
  securityId,
  UserProfile
} from '@loopback/security';

import {Task} from '../models/task.model';
import {TaskRepository} from '../repositories/task.repository';

/**
 * Abstract base class for "domain + task" controllers.
 * DomainModel is a generic type (e.g. Ott, Incident, etc.).
 */
export abstract class BaseTaskController<DomainModel extends {id?: string}> {
  constructor(
    @inject('repositories.TaskRepository')
    protected taskRepo: TaskRepository,
  ) { }

  /**
   * Each domain must implement how to create its domain record
   */
  protected abstract createDomainRecord(
    data: any,
    userId: string
  ): Promise<DomainModel>;

  /**
   * Each domain must implement how to find its domain record by ID
   */
  protected abstract findDomainRecord(
    domainId: string
  ): Promise<DomainModel>;

  /**
   * Each domain must implement how to update its domain record
   */
  protected abstract updateDomainRecord(
    domainId: string,
    updates: Partial<DomainModel>
  ): Promise<void>;

  /**
   * Optional hook: define the initial task creation logic right after
   * creating the domain record. If you return an empty object, no task is created.
   */
  protected beforeCreateTask(
    domainObj: DomainModel,
    userId: string
  ): Partial<Task> {
    // By default, we create no task. The child class can override.
    return {};
  }

  /**
   * Endpoint to (1) create domain record, (2) create an initial Task if needed.
   */
  @post('/create-domain-with-task')
  @response(200, {
    description: 'Create domain object + initial task (if any)',
  })
  async createDomainWithTask(
    @inject(SecurityBindings.USER, {optional: true})
    currentUser: UserProfile,
    @requestBody() requestData: any,
  ): Promise<DomainModel> {
    const userId = currentUser?.[securityId] ?? 'system';

    // 1) Create domain record
    const domainObj = await this.createDomainRecord(requestData, userId);

    // 2) Possibly create a new Task
    const initialTaskData = this.beforeCreateTask(domainObj, userId);
    if (initialTaskData.taskType) {
      // Fill in some defaults
      const task: Partial<Task> = {
        status: 'Initiated',
        submittedById: userId,
        ...initialTaskData,
      };
      await this.taskRepo.create(task);
    }

    return domainObj;
  }

  /**
   * Endpoint to submit a task, marking it "Completed" and optionally creating a next task,
   * plus updating the domain record if needed.
   */
  @patch('/{domainId}/submit-task/{taskId}')
  @response(204, {
    description: 'Submit a task for the domain object',
  })
  async submitTask(
    @inject(SecurityBindings.USER, {optional: true})
    currentUser: UserProfile,
    @param.path.string('domainId') domainId: string,
    @param.path.string('taskId') taskId: string,
    @requestBody({
      description: 'Payload for completing and optionally chaining new tasks + domain updates',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              nextTaskType: {type: 'string'},
              assignedToId: {type: 'array', items: {type: 'string'}},
              domainUpdates: {type: 'object'},
            },
          },
        },
      },
    })
    body: {
      nextTaskType?: string;
      assignedToId?: string;
      domainUpdates?: Partial<DomainModel>;
    }
  ): Promise<void> {
    const userId = currentUser?.[securityId] ?? 'system';

    // 1) Mark the current task as "Completed"
    await this.taskRepo.updateById(taskId, {status: 'Completed'});

    // 2) Optionally create a new follow-up task
    if (body.nextTaskType && body.assignedToId) {
      await this.taskRepo.create({
        taskType: body.nextTaskType,
        status: 'Initiated',
        assignedToId: body.assignedToId,
        submittedById: userId,
      });
    }

    // 3) If the domain needs updating (status changes, etc.), do so
    if (body.domainUpdates) {
      await this.updateDomainRecord(domainId, body.domainUpdates);
    }
  }
}
