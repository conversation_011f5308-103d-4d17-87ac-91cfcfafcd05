import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  WorkActivity,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentWorkActivityController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/work-activity', {
    responses: {
      '200': {
        description: 'WorkActivity belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(WorkActivity),
          },
        },
      },
    },
  })
  async getWorkActivity(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<WorkActivity> {
    return this.incidentRepository.workActivity(id);
  }
}
