import {Entity, model, property} from '@loopback/repository';

@model()
export class UserAssignment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  userId: string;

  @property({
    type: 'string',
    required: true,
  })
  roleId: string;

  @property({
    type: 'string',
    required: true,
  })
  locationId: string;

  @property({
    type: 'string',
    required: true,
  })
  serviceId: string;

  @property({
    type: 'boolean',
    default: false,
  })
  allSubLocations?: boolean;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<UserAssignment>) {
    super(data);
  }
}

export interface UserAssignmentRelations {
  // describe navigational properties here
}

export type UserAssignmentWithRelations = UserAssignment & UserAssignmentRelations;
