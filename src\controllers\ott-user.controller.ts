import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Ott,
  User,
} from '../models';
import {OttRepository} from '../repositories';

export class OttUserController {
  constructor(
    @repository(OttRepository)
    public ottRepository: OttRepository,
  ) { }

  @get('/otts/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Ott',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Ott.prototype.id,
  ): Promise<User> {
    return this.ottRepository.creator(id);
  }
}
