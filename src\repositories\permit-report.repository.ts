import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {Action, LocationFive, LocationFour, LocationOne, LocationSix, LocationThree, LocationTwo, PermitReport, PermitReportRelations, User, RiskAssessment} from '../models';
import {ActionRepository} from './action.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationSixRepository} from './location-six.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationTwoRepository} from './location-two.repository';
import {UserRepository} from './user.repository';
import {RiskAssessmentRepository} from './risk-assessment.repository';

export class PermitReportRepository extends DefaultCrudRepository<
  PermitReport,
  typeof PermitReport.prototype.id,
  PermitReportRelations
> {

  public readonly applicant: BelongsToAccessor<User, typeof PermitReport.prototype.id>;

  public readonly assessor: BelongsToAccessor<User, typeof PermitReport.prototype.id>;

  public readonly approver: BelongsToAccessor<User, typeof PermitReport.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof PermitReport.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof PermitReport.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof PermitReport.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof PermitReport.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof PermitReport.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof PermitReport.prototype.id>;

  public readonly permitReportActions: HasManyRepositoryFactory<Action, typeof PermitReport.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof PermitReport.prototype.id>;

  public readonly acknowledger: BelongsToAccessor<User, typeof PermitReport.prototype.id>;

  public readonly riskAssessment: BelongsToAccessor<RiskAssessment, typeof PermitReport.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>, @repository.getter('RiskAssessmentRepository') protected riskAssessmentRepositoryGetter: Getter<RiskAssessmentRepository>,
  ) {
    super(PermitReport, dataSource);
    this.riskAssessment = this.createBelongsToAccessorFor('riskAssessment', riskAssessmentRepositoryGetter,);
    this.registerInclusionResolver('riskAssessment', this.riskAssessment.inclusionResolver);
    this.acknowledger = this.createBelongsToAccessorFor('acknowledger', userRepositoryGetter,);
    this.registerInclusionResolver('acknowledger', this.acknowledger.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.permitReportActions = this.createHasManyRepositoryFactoryFor('permitReportActions', actionRepositoryGetter,);
    this.registerInclusionResolver('permitReportActions', this.permitReportActions.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.approver = this.createBelongsToAccessorFor('approver', userRepositoryGetter,);
    this.registerInclusionResolver('approver', this.approver.inclusionResolver);
    this.assessor = this.createBelongsToAccessorFor('assessor', userRepositoryGetter,);
    this.registerInclusionResolver('assessor', this.assessor.inclusionResolver);
    this.applicant = this.createBelongsToAccessorFor('applicant', userRepositoryGetter,);
    this.registerInclusionResolver('applicant', this.applicant.inclusionResolver);
  }
}
