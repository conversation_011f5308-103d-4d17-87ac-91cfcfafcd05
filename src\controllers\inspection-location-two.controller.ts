import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  LocationTwo,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionLocationTwoController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to Inspection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<LocationTwo> {
    return this.inspectionRepository.locationTwo(id);
  }
}
