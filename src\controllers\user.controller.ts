import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  property,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';

import {SNS} from 'aws-sdk';
import {User} from '../models';
import {ConfigRepository, RoleRepository, UserLocationRoleRepository, UserRepository} from '../repositories';

import {SecurityBindings, UserProfile} from '@loopback/security';
import {generateRandomPassword} from '../utils/passwordUtils';

import {authenticate} from '@loopback/authentication';
import {OPERATION_SECURITY_SPEC} from '@loopback/authentication-jwt';
import {CognitoIdentityServiceProvider} from 'aws-sdk';
import {SqsService} from '../services/sqs-service.service';

export class DeviceTokenRequest {
  @property({
    type: 'string',
    required: true,
  })
  deviceToken: string;
}

export class PasswordResetRequest {
  @property({
    type: 'string',
    required: true,
  })
  email: string;
}


@authenticate('cognito-jwt')
export class UserController {

  public cognito: CognitoIdentityServiceProvider;
  private cognitoInitialized: Promise<void>;

  constructor(

    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(RoleRepository)
    public roleRepository: RoleRepository,
    @repository(ConfigRepository)
    public configRepository: ConfigRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) {

    this.cognitoInitialized = this.initializeCognito();
  }

  async initializeCognito() {
    const region = await this.configRepository.getConfigValue('COGNITO_REGION');
    const accessKeyId = await this.configRepository.getConfigValue('AWS_COGNITO_ACCESS_KEY');
    const secretAccessKey = await this.configRepository.getConfigValue('AWS_COGNITO_SECRET_KEY');

    this.cognito = new CognitoIdentityServiceProvider({
      region,
      accessKeyId,
      secretAccessKey,
    });


  }

  private async ensureCognitoInitialized(): Promise<void> {
    if (!this.cognito) {
      await this.cognitoInitialized;
    }
  }

  @post('/users')
  @response(200, {
    description: 'User model instance',
    content: {'application/json': {schema: getModelSchemaRef(User)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {
            title: 'NewUser',
            exclude: ['id'],
          }),
        },
      },
    })
    user: Omit<User, 'id'>,
  ): Promise<User> {
    return this.userRepository.create(user);
  }




  @get('/users/count')
  @response(200, {
    description: 'User model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(User) where?: Where<User>,
  ): Promise<Count> {
    return this.userRepository.count(where);
  }

  @get('/users')
  @response(200, {
    description: 'Array of User model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(User, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(User) filter?: Filter<User>,
  ): Promise<User[]> {
    return this.userRepository.find(filter);
  }

  @patch('/users')
  @response(200, {
    description: 'User PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {partial: true}),
        },
      },
    })
    user: User,
    @param.where(User) where?: Where<User>,
  ): Promise<Count> {
    return this.userRepository.updateAll(user, where);
  }

  @get('/users/{id}')
  @response(200, {
    description: 'User model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(User, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(User, {exclude: 'where'}) filter?: FilterExcludingWhere<User>
  ): Promise<User> {
    return this.userRepository.findById(id, filter);
  }

  // @get('/users/me')
  // @response(200, {
  //   description: 'User model instance',

  // })
  // async findUserMe(
  //   @inject(SecurityBindings.USER)
  //   currentUserProfile: UserProfile,
  // ): Promise<User> {
  //   const email = currentUserProfile.email;
  //   const user = await this.userRepository.findOne({where: {email: email}});
  //   if (!user) {
  //     throw new Error('Unauthorized')
  //   }
  //   return user;

  // }

  @patch('/users/{id}')
  @response(204, {
    description: 'User PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {partial: true}),
        },
      },
    })
    user: User,
  ): Promise<void> {
    await this.userRepository.updateById(id, user);
  }

  @put('/users/{id}')
  @response(204, {
    description: 'User PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() user: User,
  ): Promise<void> {
    await this.userRepository.replaceById(id, user);
  }

  @del('/users/{id}')
  @response(204, {
    description: 'User DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userRepository.deleteById(id);
  }

  @post('/users/get_users', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  // @authenticate('jwt')
  async findUsers(

    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              locationOneId: {type: 'string'},
              locationTwoId: {type: 'string'},
              locationThreeId: {type: 'string'},
              locationFourId: {type: 'string'},
              mode: {type: 'string'}
            },
            required: ['locationOneId', 'locationTwoId', 'locationThreeId', 'locationFourId', 'mode'],
          },
        },
      },
    })
    payload: {
      locationOneId: string;
      locationTwoId: string;
      locationThreeId: string;
      locationFourId: string;
      mode: string;
    }
  ): Promise<User[]> {

    const roleId = await this.roleRepository.findOne({where: {maskId: payload.mode}});

    if (!roleId) {throw new HttpErrors.NotFound('Role not found')}
    const whereCondition = {
      roles: {inq: [[roleId.id]]},
      or: [
        {
          locationOneId: {inq: [payload.locationOneId]},
          locationTwoId: {inq: [payload.locationTwoId]},
          locationThreeId: {inq: [payload.locationThreeId]},
          locationFourId: {inq: [payload.locationFourId]},
        },
        {
          locationOneId: {inq: ['tier1-all']},
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: {inq: [payload.locationOneId]},
          locationTwoId: {inq: ['tier2-all']},
          locationThreeId: '',
          locationFourId: '',
        },
        {
          locationOneId: {inq: [payload.locationOneId]},
          locationTwoId: {inq: [payload.locationTwoId]},
          locationThreeId: {inq: ['tier3-all']},
          locationFourId: '',
        },
        {
          locationOneId: {inq: [payload.locationOneId]},
          locationTwoId: {inq: [payload.locationTwoId]},
          locationThreeId: {inq: [payload.locationThreeId]},
          locationFourId: {inq: ['tier4-all']},
        },
      ]
    };

    const userLocationRoles = await this.userLocationRoleRepository.find({
      where: whereCondition,
    });

    const userIds = userLocationRoles.map((userLocationRole) => userLocationRole.userId);
    const uniqueUserIds = Array.from(new Set(userIds));

    const users = await this.userRepository.find({
      where: {id: {inq: uniqueUserIds as string[]}},
    });

    return users;
  }

  @patch('/users-signature')
  @response(204, {
    description: 'User PATCH success',
  })
  async updateSignatureById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {partial: true}),
        },
      },
    })
    user: User,
  ): Promise<void> {
    const {email} = currentUserProfile;

    const userData = await this.userRepository.findOne({where: {email}});
    if (!userData) {
      throw new HttpErrors.Unauthorized('UnAuthorized');
    }
    await this.userRepository.updateById(userData.id, {signature: user.signature});
  }

  @get('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',

      },
    },
  })
  // @authenticate('jwt')
  async getCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<any> {
    // (@jannyHou)FIXME: explore a way to generate OpenAPI schema for symbol property

    const {email} = currentUserProfile;

    const user = await this.userRepository.findOne({where: {email}, include: [{relation: 'userLocation'}]});
    if (!user) {
      throw new HttpErrors.Unauthorized('UnAuthorized');
    }

    const userRoles = await this.userLocationRoleRepository.find({where: {userId: user.id}});
    const uniqueRolesArray = Array.from(new Set(userRoles.map(role => role.roles).flat()));

    const roles = await this.roleRepository.find({
      where: {id: {inq: uniqueRolesArray}},
      fields: {
        id: true,
        name: true,
        maskId: true,
        maskName: true
      }
    });




    return {id: user.id, email: user.email, firstName: user.firstName, roles: roles, type: user.type, signature: user.signature};
  }

  //Added users me
  @post('/users/me', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',

      },
    },

  })

  async printCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeviceTokenRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    deviceTokenRequest: DeviceTokenRequest,
  ): Promise<any> {
    console.log(deviceTokenRequest, ' Checking this out');
    const cognitoRegion = await this.configRepository.getConfigValue('COGNITO_REGION');
    const awsAccessKeyId = await this.configRepository.getConfigValue('AWS_ACCESS_KEY_ID');
    const awsSecretAccessKey = await this.configRepository.getConfigValue('AWS_SECRET_ACCESS_KEY');
    const awsSnsTopicArn = await this.configRepository.getConfigValue('AWS_SNS_TOPIC_ARN');

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}, include: [{relation: 'userLocation'}]});
    if (user) {

      if (!user.deviceToken || user.deviceToken !== deviceTokenRequest.deviceToken) {

        // Replace with your platform application ARN
        const sns = new SNS({
          region: cognitoRegion,
          accessKeyId: awsAccessKeyId,
          secretAccessKey: awsSecretAccessKey
        });
        try {
          const createEndpointResponse = await sns
            .createPlatformEndpoint({
              PlatformApplicationArn: `${awsSnsTopicArn}`,
              Token: deviceTokenRequest.deviceToken
            })
            .promise();

          const endpointArn = createEndpointResponse.EndpointArn;
          console.log(deviceTokenRequest.deviceToken, endpointArn, ' Check this out');
          await this.userRepository.updateById(user?.id, {deviceToken: deviceTokenRequest.deviceToken, arn: endpointArn})
        }
        catch (error) {
          console.log(error)
          throw new Error('Failed to create endpoint');

        }

      }

      const userRoles = await this.userLocationRoleRepository.find({where: {userId: user.id}});
      const uniqueRolesArray = Array.from(new Set(userRoles.map(role => role.roles).flat()));

      const roles = await this.roleRepository.find({
        where: {id: {inq: uniqueRolesArray}},
        fields: {
          id: true,
          name: true,
          maskId: true,
          maskName: true
        }
      });


      return {id: user.id, email: user.email, firstName: user.firstName, roles: roles, type: user.type, signature: user.signature};

    } else {
      throw new HttpErrors.Unauthorized('Unauthorized');
    }

  }

  @post('/users/external', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async createExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(User, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: User,
  ): Promise<User> {
    await this.ensureCognitoInitialized();

    newUserRequest.roles = ['user'];


    try {

      const tempPassword = generateRandomPassword();

      const sanitizedUsername = newUserRequest.email?.replace(/@/g, '_at_')
        .replace(/\./g, '_dot_');
      const COGNITO_USER_POOL_ID = await this.configRepository.getConfigValue('COGNITO_USER_POOL_ID')
      const params: CognitoIdentityServiceProvider.AdminCreateUserRequest = {
        UserPoolId: `${COGNITO_USER_POOL_ID}`,
        Username: `${sanitizedUsername}`,
        TemporaryPassword: tempPassword,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: newUserRequest.email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      const awsNewUser = await this.cognito.adminCreateUser(params).promise();
      const userAttributes = awsNewUser.User?.Attributes;
      if (userAttributes) {
        const subAttribute = userAttributes.find(attr => attr.Name === 'sub');

        const sub = subAttribute?.Value;
        newUserRequest.id = sub;

        const newUser = await this.userRepository.create(newUserRequest);
        await this.sqsService.sendMessage(newUser, 'Account has been created', `Please login with your email and given password ${tempPassword}`);
        return newUser;

      } else {
        throw new HttpErrors.NotFound(`User not created. Try again`);
      }

    } catch (error) {
      // MongoError 11000 duplicate key
      if (error.code === 11000 && error.errmsg.includes('index: uniqueEmail')) {
        throw new HttpErrors.Conflict('Email value is already taken');
      } else {
        throw error;
      }
    }
  }

  @post('/users/external/reset-password', {
    responses: {
      '200': {
        description: 'Password Reset Success',
        content: {'application/json': {schema: {type: 'object'}}},
      },
    },
  })

  async resetPasswordExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PasswordResetRequest, {
            title: 'PasswordReset',
          }),
        },
      },
    })
    passwordResetRequest: PasswordResetRequest,
  ): Promise<unknown> {
    // All new users have the "customer" role by default

    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {

      const tempPassword = generateRandomPassword();
      const email = passwordResetRequest.email
      const COGNITO_USER_POOL_ID = await this.configRepository.getConfigValue('COGNITO_USER_POOL_ID')
      const params = {
        UserPoolId: `${COGNITO_USER_POOL_ID}`,
        Username: email,
        Password: tempPassword,
        Permanent: false
      };

      // await cognito.adminSetUserMFAPreference({
      //   UserPoolId: `${process.env.AWS_EXTERNAL_USER_POOL_ID}`,
      //   Username: email,
      //   SoftwareTokenMfaSettings: { Enabled: false, PreferredMfa: false },
      // }).promise();

      const awsNewUser = await this.cognito.adminSetUserPassword(params).promise();
      await this.sqsService.sendEmail(email, 'Account Password has been created', `Please login with your email and given password ${tempPassword}`);

      if (awsNewUser) {
        return {message: 'Password updated successfully'};
      } else {
        throw new HttpErrors.Conflict('Email value is already taken');
      }

    } catch (error) {
      return error
    }

  }

  @post('/users/external/reset-mfa', {
    responses: {
      '200': {
        description: 'MFA Reset Success',
        content: {'application/json': {schema: {type: 'object'}}},
      },
    },
  })

  async resetMFAExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PasswordResetRequest, {
            title: 'PasswordReset',
          }),
        },
      },
    })
    passwordResetRequest: PasswordResetRequest,
  ): Promise<unknown> {
    // All new users have the "customer" role by default
    const tempPassword = generateRandomPassword();
    const email = passwordResetRequest.email
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));
    const COGNITO_USER_POOL_ID = await this.configRepository.getConfigValue('COGNITO_USER_POOL_ID');

    try {

      await this.ensureCognitoInitialized();
      await this.cognito.adminDeleteUser({
        UserPoolId: `${COGNITO_USER_POOL_ID}`,
        Username: email // The username or email of the user to delete
      }).promise();
    } catch (error) {
      console.error("Failed to delete user:", error);
      throw new Error("Failed to delete user.");
    }
    try {

      const sanitizedUsername = email?.replace(/@/g, '_at_')
        .replace(/\./g, '_dot_');

      if (!sanitizedUsername) {
        throw new Error('Sanitized username is undefined.');
      }
      // Create the user with the same username (email) and temporary password
      const params = {
        UserPoolId: `${COGNITO_USER_POOL_ID}`,
        Username: sanitizedUsername,
        TemporaryPassword: tempPassword,
        MessageAction: 'SUPPRESS',
        UserAttributes: [
          {
            Name: 'email',
            Value: email
          },
          {
            Name: 'email_verified',
            Value: 'true'
          }
        ]
      };

      await this.cognito.adminCreateUser(params).promise();

      // Optionally, set the user's password (if you don't want it to be the temporary one) and enable MFA here

    } catch (error) {
      console.error("Failed to create user:", error);
      throw new Error("Failed to create user.");
    }

    await this.sqsService.sendEmail(email, 'User information has been reset', `Please login with your email and given password: ${tempPassword}`);
    return {message: 'MFA Reset updated successfully'};


  }


  @post('/users/external/delete', {
    responses: {
      '200': {
        description: 'Delete User Success',
        content: {'application/json': {schema: {type: 'object'}}},
      },
    },
  })

  async deleteExternal(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PasswordResetRequest, {
            title: 'PasswordReset',
          }),
        },
      },
    })
    passwordResetRequest: PasswordResetRequest,
  ): Promise<unknown> {
    // All new users have the "customer" role by default

    const email = passwordResetRequest.email
    // ensure a valid email value and password value
    // validateCredentials(_.pick(newUserRequest, ['email', 'password']));
    const user = await this.userRepository.findOne({where: {email: email, type: 'External'}});
    if (!user) throw new Error('User not found')
    await this.userRepository.updateById(user.id, {status: false})

    if (!email) return true;
    const COGNITO_USER_POOL_ID = await this.configRepository.getConfigValue('COGNITO_USER_POOL_ID');

    try {
      await this.ensureCognitoInitialized();
      await this.cognito.adminDeleteUser({
        UserPoolId: `${COGNITO_USER_POOL_ID}`,
        Username: email // The username or email of the user to delete
      }).promise();



    } catch (error) {
      console.error("Failed to delete user:", error);
      throw new Error("Failed to delete user.");
    }


    return {message: 'User Deleted'};


  }

  @get('/users/external', {

    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })

  async findExternalUser(): Promise<User[]> {
    return this.userRepository.find({where: {type: {nin: ['AD']}}});
  }
}
