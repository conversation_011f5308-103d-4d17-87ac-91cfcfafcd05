import {inject} from '@loopback/core';
import {DefaultCrudRepository, juggler} from '@loopback/repository';
import {EquipmentCategory, EquipmentCategoryRelations} from '../models';

export class EquipmentCategoryRepository extends DefaultCrudRepository<
  EquipmentCategory,
  typeof EquipmentCategory.prototype.id,
  EquipmentCategoryRelations
> {
  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
  ) {
    super(EquipmentCategory, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
