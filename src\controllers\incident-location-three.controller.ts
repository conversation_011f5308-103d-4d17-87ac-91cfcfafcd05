import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  LocationThree,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentLocationThreeController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<LocationThree> {
    return this.incidentRepository.locationThree(id);
  }
}
