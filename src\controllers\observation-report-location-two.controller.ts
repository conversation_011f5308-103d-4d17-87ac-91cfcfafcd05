import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  LocationTwo,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportLocationTwoController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<LocationTwo> {
    return this.observationReportRepository.locationTwo(id);
  }
}
