import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor} from '@loopback/repository';
import {NearTermControlMeasures, NearTermControlMeasuresRelations, User, Incident} from '../models';
import {UserRepository} from './user.repository';
import {IncidentRepository} from './incident.repository';

export class NearTermControlMeasuresRepository extends DefaultCrudRepository<
  NearTermControlMeasures,
  typeof NearTermControlMeasures.prototype.id,
  NearTermControlMeasuresRelations
> {

  public readonly user: BelongsToAccessor<User, typeof NearTermControlMeasures.prototype.id>;

  public readonly incident: BelongsToAccessor<Incident, typeof NearTermControlMeasures.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof NearTermControlMeasures.prototype.id>;

  public readonly approver: BelongsToAccessor<User, typeof NearTermControlMeasures.prototype.id>;

  public readonly responsible: BelongsToAccessor<User, typeof NearTermControlMeasures.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('IncidentRepository') protected incidentRepositoryGetter: Getter<IncidentRepository>,
  ) {
    super(NearTermControlMeasures, dataSource);
    this.responsible = this.createBelongsToAccessorFor('responsible', userRepositoryGetter,);
    this.registerInclusionResolver('responsible', this.responsible.inclusionResolver);
    this.approver = this.createBelongsToAccessorFor('approver', userRepositoryGetter,);
    this.registerInclusionResolver('approver', this.approver.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.incident = this.createBelongsToAccessorFor('incident', incidentRepositoryGetter,);
    this.registerInclusionResolver('incident', this.incident.inclusionResolver);
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
