import {belongsTo, Entity, model, property} from '@loopback/repository';
import {Ott} from './ott.model';
import {User} from './user.model';

@model()
export class OttTask extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;
  @property({
    type: 'string',
  })
  reviewerComments?: string;
  @property({
    type: 'array',
    itemType: 'string'
  })
  img?: string[];

  @property({
    type: 'array',
    itemType: 'string'
  })
  evidence?: string[];

  @property({
    type: 'boolean',
    default: false,
  })
  checked?: boolean;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  status?: 'Yet to Start' | 'Completed' | 'On Hold' | 'Waiting' | 'In Progress';

  @property({
    type: 'string',
  })
  remarks?: string;

  @belongsTo(() => Ott)
  ottId: string;

  @belongsTo(() => User)
  dependentId: string;

  constructor(data?: Partial<OttTask>) {
    super(data);
  }
}

export interface OttTaskRelations {
  // describe navigational properties here
}

export type OttTaskWithRelations = OttTask & OttTaskRelations;
