import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {Checklist} from '../models';
import {ChecklistRepository, UserRepository} from '../repositories';
const SERVICE_NAME = 'CL'; // Checklist service prefix

@authenticate('cognito-jwt')
export class ChecklistController {
  constructor(
    @repository(ChecklistRepository)
    public checklistRepository: ChecklistRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/checklists')
  @response(200, {
    description: 'Checklist model instance',
    content: {'application/json': {schema: getModelSchemaRef(Checklist)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {
            title: 'NewChecklist',
            exclude: ['id'],
          }),
        },
      },
    })
    checklist: Omit<Checklist, 'id'>,
  ): Promise<Checklist> {
    // Format version as DDMMYY
    const today = new Date();
    const day = today.getDate().toString().padStart(2, '0');
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const year = today.getFullYear().toString().slice(-2);

    checklist.version = `${day}${month}${year}`;

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new Error(`User not found with this email: ${email}`);
    }

    // Set curatorId to the submitted user's ID
    checklist.curatorId = user.id ?? '';

    // Generate maskId
    const count = await this.checklistRepository.count();


    // Format: CL-YYYYMMDD-count+1
    const fullYear = today.getFullYear();
    const formattedMonth = (today.getMonth() + 1).toString().padStart(2, '0');
    const formattedDay = today.getDate().toString().padStart(2, '0');

    checklist.maskId = `${SERVICE_NAME}-${fullYear}${formattedMonth}${formattedDay}-${count.count + 1}`;

    // Set default status to Draft if not provided
    if (!checklist.status) {
      checklist.status = 'Draft';
    }

    return this.checklistRepository.create(checklist);
  }

  @get('/checklists/count')
  @response(200, {
    description: 'Checklist model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Checklist) where?: Where<Checklist>,
  ): Promise<Count> {
    return this.checklistRepository.count(where);
  }

  @get('/checklists')
  @response(200, {
    description: 'Array of Checklist model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Checklist, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Checklist) filter?: Filter<Checklist>,
  ): Promise<Checklist[]> {
    return this.checklistRepository.find(filter);
  }

  @patch('/checklists')
  @response(200, {
    description: 'Checklist PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {partial: true}),
        },
      },
    })
    checklist: Checklist,
    @param.where(Checklist) where?: Where<Checklist>,
  ): Promise<Count> {
    return this.checklistRepository.updateAll(checklist, where);
  }

  @get('/checklists/{id}')
  @response(200, {
    description: 'Checklist model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Checklist, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Checklist, {exclude: 'where'}) filter?: FilterExcludingWhere<Checklist>
  ): Promise<Checklist> {
    return this.checklistRepository.findById(id, filter);
  }

  @patch('/checklists/{id}')
  @response(204, {
    description: 'Checklist PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Checklist, {partial: true}),
        },
      },
    })
    checklist: Checklist,
  ): Promise<void> {
    await this.checklistRepository.updateById(id, checklist);
  }

  @put('/checklists/{id}')
  @response(204, {
    description: 'Checklist PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() checklist: Checklist,
  ): Promise<void> {
    await this.checklistRepository.replaceById(id, checklist);
  }

  @del('/checklists/{id}')
  @response(204, {
    description: 'Checklist DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.checklistRepository.deleteById(id);
  }
}
