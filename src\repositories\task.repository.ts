import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {Task, TaskRelations, User} from '../models';
import {UserRepository} from './user.repository';

export class TaskRepository extends DefaultCrudRepository<
  Task,
  typeof Task.prototype.id,
  TaskRelations
> {

  public readonly submittedBy: BelongsToAccessor<User, typeof Task.prototype.id>;

  public readonly assignedTo: BelongsToAccessor<User, typeof Task.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(Task, dataSource);
    this.assignedTo = this.createBelongsToAccessorFor('assignedTo', userRepositoryGetter,);
    this.registerInclusionResolver('assignedTo', this.assignedTo.inclusionResolver);
    this.submittedBy = this.createBelongsToAccessorFor('submittedBy', userRepositoryGetter,);
    this.registerInclusionResolver('submittedBy', this.submittedBy.inclusionResolver);
  }
}
