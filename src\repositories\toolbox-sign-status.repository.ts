import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {ToolboxSignStatus, ToolboxSignStatusRelations, User} from '../models';
import {UserRepository} from './user.repository';

export class ToolboxSignStatusRepository extends DefaultCrudRepository<
  ToolboxSignStatus,
  typeof ToolboxSignStatus.prototype.id,
  ToolboxSignStatusRelations
> {

  public readonly signedBy: BelongsToAccessor<User, typeof ToolboxSignStatus.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(ToolboxSignStatus, dataSource);
    this.signedBy = this.createBelongsToAccessorFor('signedBy', userRepositoryGetter,);
    this.registerInclusionResolver('signedBy', this.signedBy.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
