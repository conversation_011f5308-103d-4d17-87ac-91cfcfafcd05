import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {OttTask} from '../models';
import {OttTaskRepository} from '../repositories';

export class OttTaskController {
  constructor(
    @repository(OttTaskRepository)
    public ottTaskRepository : OttTaskRepository,
  ) {}

  @post('/ott-tasks')
  @response(200, {
    description: 'OttTask model instance',
    content: {'application/json': {schema: getModelSchemaRef(OttTask)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OttTask, {
            title: 'NewOttTask',
            exclude: ['id'],
          }),
        },
      },
    })
    ottTask: Omit<OttTask, 'id'>,
  ): Promise<OttTask> {
    return this.ottTaskRepository.create(ottTask);
  }

  @get('/ott-tasks/count')
  @response(200, {
    description: 'OttTask model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(OttTask) where?: Where<OttTask>,
  ): Promise<Count> {
    return this.ottTaskRepository.count(where);
  }

  @get('/ott-tasks')
  @response(200, {
    description: 'Array of OttTask model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(OttTask, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(OttTask) filter?: Filter<OttTask>,
  ): Promise<OttTask[]> {
    return this.ottTaskRepository.find(filter);
  }

  @patch('/ott-tasks')
  @response(200, {
    description: 'OttTask PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OttTask, {partial: true}),
        },
      },
    })
    ottTask: OttTask,
    @param.where(OttTask) where?: Where<OttTask>,
  ): Promise<Count> {
    return this.ottTaskRepository.updateAll(ottTask, where);
  }

  @get('/ott-tasks/{id}')
  @response(200, {
    description: 'OttTask model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(OttTask, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(OttTask, {exclude: 'where'}) filter?: FilterExcludingWhere<OttTask>
  ): Promise<OttTask> {
    return this.ottTaskRepository.findById(id, filter);
  }

  @patch('/ott-tasks/{id}')
  @response(204, {
    description: 'OttTask PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OttTask, {partial: true}),
        },
      },
    })
    ottTask: OttTask,
  ): Promise<void> {
    await this.ottTaskRepository.updateById(id, ottTask);
  }

  @put('/ott-tasks/{id}')
  @response(204, {
    description: 'OttTask PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() ottTask: OttTask,
  ): Promise<void> {
    await this.ottTaskRepository.replaceById(id, ottTask);
  }

  @del('/ott-tasks/{id}')
  @response(204, {
    description: 'OttTask DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.ottTaskRepository.deleteById(id);
  }
}
