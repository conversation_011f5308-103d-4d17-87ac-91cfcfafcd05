import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  model,
  property,
  repository,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, RiskAssessment} from '../models';
import {ActionRepository, DepartmentRepository, RaTeamMemberRepository, RiskAssessmentRepository, ServiceRepository, UserRepository, WorkActivityRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {ControlOption, HighRiskHazard} from '../types/HighRiskHazard';
export type notification = {
  depart: {label: string, value: string},
  activity: {label: string, value: string},
  member: any[],
  leader: string
}

const SERVICE_NAME = 'RA';

@model()
export class RiskAssessmentWithRaTeamMember extends RiskAssessment {
  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: {
      type: 'object',
      properties: {


        id: {type: 'string'},
        name: {type: 'string'},


      },
    },
  })
  raTeamMembersList?: {id: string; name: string}[];
}

@authenticate('cognito-jwt')
export class RiskAssessmentController {
  constructor(
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
    @repository(RaTeamMemberRepository)
    public raTeamMemberRepository: RaTeamMemberRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @repository(WorkActivityRepository)
    public workActivityRepository: WorkActivityRepository,
    @repository(DepartmentRepository)
    public departmentRepository: DepartmentRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/risk-assessments')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember)
      },
    },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: Omit<RiskAssessmentWithRaTeamMember, 'id'>,
  ): Promise<RiskAssessment> {
    const {raTeamMembersList, ...raRequest} = riskAssessment;
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.riskAssessmentRepository.count();
    let prefix = SERVICE_NAME;
    if (raRequest.type === 'High-Risk Hazard') {
      prefix = `CH${prefix}`;
    }
    raRequest.maskId = `${prefix}-${year}${month}${day}-${count.count + 1}`;

    // Generate GMT date one year from now
    // raRequest.nextReviewDate = moment().add(1, 'year').utc().format('YYYY-MM-DDTHH:mm:ssZ');

    const RA = await this.riskAssessmentRepository.create(raRequest);
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    if (raTeamMembersList && raTeamMembersList.length > 0) {
      const createdRaTeamMembers = [];
      for (const member of raTeamMembersList) {
        const createdMember = await this.raTeamMemberRepository.create({
          userId: member.id ?? '',
          riskAssessmentId: RA.id,
          status: 'Initiated'
        });
        createdRaTeamMembers.push(createdMember);
      }

      const actions: Partial<Action>[] = createdRaTeamMembers.map(member => {
        return {
          application: SERVICE_NAME,
          actionType: 'sign',
          actionToBeTaken: 'Confirm my participation in this Risk Assessment as a team member',
          description: 'Confirm my participation in this Risk Assessment as a team member',
          maskId: raRequest.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'RA-SIGN',
          applicationId: RA.id,
          objectId: member.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [member.userId],
          submitURL: '/ra-team-member-submit-signature',
          status: 'Initiated',
          serviceId: service.id
        };
      });

      // Insert into actionRepository
      await this.actionRepository.createAll(actions);
    }

    //Send Mail
    if (raTeamMembersList && raTeamMembersList.length > 0) {

      for (const member of raTeamMembersList) {
        const users = await this.userRepository.findById(member.id)
        if (!users || !users.email) {
          throw new Error('User not found!')
        }
        const mailSubject = `Notification - Publication of Draft - ${RA.maskId}`;
        let mailBody = ``
        if (RA.type === 'Non Routine') {
          mailBody = `<h4>Dear ${users.firstName},</h4>

        <p>Thank you for your participation in the above RA for <b>${RA.nonRoutineWorkActivity}</b></p>

         <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>

         <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
           <i>This email is an automated notification. Please do not reply to this message.</i>



         `;
        }

        if (RA.type === 'Routine') {

          const workActivity = await this.workActivityRepository.findById(RA.workActivityId)
          const department = await this.departmentRepository.findById(RA.departmentId)

          if (!workActivity || !department) {
            throw new Error('No work activity and department found')
          }
          mailBody = `<h4>Dear ${users.firstName},</h4>

          <p>Thank you for your participation in the above RA for <b>${workActivity?.name}</b> in <b>${department?.name}</b> Department. </p>

           <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>

           <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
             <i>This email is an automated notification. Please do not reply to this message.</i>
           `;
        }
        await this.sqsService.sendMessage(users, mailSubject, mailBody);

      }
    }
    return RA;
  }

  @post('/risk-member-notification')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {'application/json': {schema: getModelSchemaRef(RiskAssessment)}},
  })
  async riskMemberNotification(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              depart: {
                type: 'object',
                properties: {
                  label: {type: 'string'},
                  value: {type: 'string'},
                },
                required: ['label', 'value'],
              },
              activity: {
                type: 'object',
                properties: {
                  label: {type: 'string'},
                  value: {type: 'string'},
                },
                required: ['label', 'value'],
              },
              member: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    label: {type: 'string'},
                    department: {type: 'string'},
                    value: {type: 'string'},
                  },
                  required: ['label', 'department', 'value'],
                },
              },
              leader: {type: 'string'},
            },
            required: ['depart', 'activity', 'member', 'leader'],
          },
        },
      },
    })
    riskAssessment: notification,
  ): Promise<boolean> {



    for (let i = 0; i < riskAssessment.member.length; i++) {
      const mailSubject = `Notification - Risk Assessment Team`;
      let mailBody = `<h4>Dear ${riskAssessment.member[i].label},</h4>
    <p>Congratulations! You've been selected for our Risk Assessment team for <b>${riskAssessment.activity.label}</b> performed by the <b>${riskAssessment.depart.label}</b> Department with the undersigned as a Team Leader.  </p>
    <h5>Other team members for this are:</h5>`;
      mailBody += '<ul>'
      for (let j = 0; j < riskAssessment.member.length; j++) {
        if (riskAssessment.member[i].label !== riskAssessment.member[j].label) {
          mailBody += `<li>${riskAssessment.member[j].label} ${riskAssessment.member[j].department}</li>`
        }

      }
      mailBody += '</ul>'
      mailBody += `<p> A comprehensive Risk Assessment with identification of all controls is of utmost importance in ensuring safe work activity.  We value the professional insight of team members whose collaboration and consensus contributes to the development of professional Risk Assessments.</p>`;

      mailBody += `<p> We look forward to your participation. Thank you for your efforts in advancing safety.

   </p>

<h4>${riskAssessment.leader}</h4>
<h4>RA Team Leader. </h4>
     <i>This email is an automated notification. Please do not reply to this message.</i>



   `;
      const users = await this.userRepository.findById(riskAssessment.member[i].value)
      if (!users || !users.email) {
        throw new Error('Users and email not found!')
      }
      await this.sqsService.sendMessage(users, mailSubject, mailBody);
    }

    return true;

  }

  @post('/risk-assessments-draft')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember)
      },
    },
  })
  async saveAsDraft(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: Omit<RiskAssessmentWithRaTeamMember, 'id'>,
  ): Promise<RiskAssessment> {
    const {raTeamMembersList, ...raRequest} = riskAssessment;
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.riskAssessmentRepository.count();

    raRequest.maskId = `${SERVICE_NAME}-${year}${month}${day}-${count.count + 1}`;

    // Generate GMT date one year from now
    // raRequest.nextReviewDate = moment().add(1, 'year').utc().format('YYYY-MM-DDTHH:mm:ssZ');

    const RA = await this.riskAssessmentRepository.create(raRequest);
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    if (raTeamMembersList && raTeamMembersList.length > 0) {
      const createdRaTeamMembers = [];
      for (const member of raTeamMembersList) {
        const createdMember = await this.raTeamMemberRepository.create({
          userId: member.id ?? '',
          riskAssessmentId: RA.id,
          status: 'Initiated'
        });
        createdRaTeamMembers.push(createdMember);
      }
    }

    // if (raTeamMembersList && raTeamMembersList.length > 0) {

    //   for (const member of raTeamMembersList) {
    //     const users = await this.userRepository.findById(member.id)
    //     if (!users || !users.email) {
    //       throw new Error('User not found!')
    //     }
    //     let mailSubject = `Notification - Publication of Draft - ${RA.maskId}`;
    //     let mailBody = ``
    //     if (RA.type === 'Non Routine') {
    //       mailBody = `<h4>Dear ${users.firstName},</h4>

    //     <p>Thank you for your participation in the above RA for <b>${RA.nonRoutineWorkActivity}</b></p>

    //      <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>

    //      <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
    //        <i>This email is an automated notification. Please do not reply to this message.</i>



    //      `;
    //     }

    //     if (RA.type === 'Routine') {

    //       const workActivity = await this.workActivityRepository.findById(RA.workActivityId)
    //       const department = await this.departmentRepository.findById(RA.departmentId)

    //       if (!workActivity || !department) {
    //         throw new Error('No work activity and department found')
    //       }
    //       mailBody = `<h4>Dear ${users.firstName},</h4>

    //       <p>Thank you for your participation in the above RA for <b>${workActivity?.name}</b> in <b>${department?.name}</b> Department. </p>

    //        <p> The RA Team Leader has now published the Draft Risk Assessment based on the discussions. Please review and affirm your agreement with the same so that the RA can be formalized and released. If you are not in agreement with the Draft RA, please communicate directly with the RA Team Leader and ensure conensus.  </p>

    //        <p> Note: The RA will not be considered as "Active" till such time all RA Team Members affirm their agreement to this draft. Please do so at the earliest. </p>
    //          <i>This email is an automated notification. Please do not reply to this message.</i>
    //        `;
    //     }
    //     this.sqsService.sendEmail(users.email, mailSubject, mailBody);

    //   }
    // }

    return RA;
  }

  @patch('/risk-assessments/{id}')
  @response(204, {
    description: 'RiskAssessment PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessment, {partial: true}),
        },
      },
    })
    riskAssessment: RiskAssessment,
  ): Promise<void> {
    await this.riskAssessmentRepository.updateById(id, riskAssessment);
  }


  @patch('/risk-assessments-update/{id}')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember)
      },
    },
  })
  async updateRiskAssessmentUpdate(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: Omit<RiskAssessmentWithRaTeamMember, 'id'>,
  ): Promise<void> {
    const {raTeamMembersList, ...raRequest} = riskAssessment;

    const riskAssessmentData = await this.riskAssessmentRepository.findById(id)
    await this.riskAssessmentRepository.updateById(id, raRequest);
    await this.raTeamMemberRepository.deleteAll({riskAssessmentId: id});
    await this.actionRepository.updateAll({status: 'Archived'}, {applicationId: id})
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    if (raTeamMembersList && raTeamMembersList.length > 0) {
      const createdRaTeamMembers = [];
      for (const member of raTeamMembersList) {
        const createdMember = await this.raTeamMemberRepository.create({
          userId: member.id ?? '',
          riskAssessmentId: id,
          status: 'Initiated'
        });
        createdRaTeamMembers.push(createdMember);
      }

      const actions: Partial<Action>[] = createdRaTeamMembers.map(member => {
        return {
          application: SERVICE_NAME,
          actionType: 'sign',
          actionToBeTaken: 'Confirm my participation in this Risk Assessment as a team member',
          description: 'Confirm my participation in this Risk Assessment as a team member',
          maskId: riskAssessmentData.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'RA-SIGN',
          applicationId: id,
          objectId: member.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [member.userId],
          submitURL: '/ra-team-member-submit-signature',
          status: 'Initiated',
          serviceId: service.id
        };
      });

      // Insert into actionRepository
      await this.actionRepository.createAll(actions);
    }

  }

  @patch('/risk-assessments-update-draft/{id}')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember)
      },
    },
  })
  async updateRiskAssessmentUpdateDraft(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskAssessmentWithRaTeamMember, {
            title: 'NewRiskAssessment',
            exclude: ['id'],
          }),
        },
      },
    })
    riskAssessment: Omit<RiskAssessmentWithRaTeamMember, 'id'>,
  ): Promise<void> {
    const {raTeamMembersList, ...raRequest} = riskAssessment;

    // const riskAssessmentData = await this.riskAssessmentRepository.findById(id)
    await this.riskAssessmentRepository.updateById(id, raRequest);
    await this.raTeamMemberRepository.deleteAll({riskAssessmentId: id});
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    if (raTeamMembersList && raTeamMembersList.length > 0) {
      const createdRaTeamMembers = [];
      for (const member of raTeamMembersList) {
        const createdMember = await this.raTeamMemberRepository.create({
          userId: member.id ?? '',
          riskAssessmentId: id,
          status: 'Initiated'
        });
        createdRaTeamMembers.push(createdMember);
      }

    }

  }

  @get('/risk-assessments/count')
  @response(200, {
    description: 'RiskAssessment model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(RiskAssessment) where?: Where<RiskAssessment>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.count(where);
  }

  @get('/risk-assessments')
  @response(200, {
    description: 'Array of RiskAssessment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RiskAssessment, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(RiskAssessment) filter?: Filter<RiskAssessment>,
  ): Promise<RiskAssessment[]> {
    return this.riskAssessmentRepository.find(filter);
  }

  @get('/high-risk-hazard-list')
  @response(200, {
    description: 'Array of HighRiskHazard model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {$ref: '#/components/schemas/HighRiskHazard'},
        },
      },
    },
  })
  async findHighRiskHazardList(
    @param.filter(RiskAssessment) filter?: Filter<RiskAssessment>,
  ): Promise<HighRiskHazard[]> {
    const riskAssessments = await this.riskAssessmentRepository.find(filter);

    // Map the data to only include high-risk hazards in the HighRiskHazard structure
    const highRiskHazards: HighRiskHazard[] = riskAssessments
      .filter((record: RiskAssessment) => record.type === 'High-Risk Hazard' && record.status === 'Published')
      .map((record: RiskAssessment) => {
        const hazardName = record.hazardName ?? 'Unknown Hazard'; // Fallback if hazardName is undefined

        const controls: ControlOption[] = (record.tasks || [])
          .flatMap((taskArray: any[]) =>
            taskArray.filter((task: any) => task.type === 'current_control')
          )
          .flatMap((task: any) => task.option);

        return {
          hazardName: hazardName,
          controls: controls,
          riskAssessmentId: record?.id ?? '', // Fallback if id is undefined
        };
      });

    return highRiskHazards;
  }

  @get('/routine-list')
  @response(200, {
    description: 'Array of HighRiskHazard model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {$ref: '#/components/schemas/HighRiskHazard'},
        },
      },
    },
  })
  async findRoutineList(
    @param.filter(RiskAssessment) filter?: Filter<RiskAssessment>,
  ): Promise<HighRiskHazard[]> {
    // Fetch data including the related workActivity
    const riskAssessments = await this.riskAssessmentRepository.find({
      ...filter,
      where: {
        ...filter?.where,
        type: 'Routine',
        status: 'Published',
      },
      include: [{relation: 'workActivity'}],
    });

    // Map the data to match HighRiskHazard structure
    const routineHazards: HighRiskHazard[] = riskAssessments.map((record: RiskAssessment) => {
      // Use the workActivity relation to get the hazard name, or fallback to 'Unknown Hazard'
      const hazardName = record?.workActivity?.name ?? 'Unknown Hazard';

      // Extract only current_control options from tasks
      const controls: ControlOption[] = (record.tasks || [])
        .flatMap((taskArray: {type: string; option: ControlOption[]}[]) =>
          taskArray.filter((task: any) => task.type === 'current_control')
        )
        .flatMap((task: any) => task.option);

      return {
        hazardName: hazardName,
        controls: controls,
        riskAssessmentId: record?.id ?? '', // Fallback if id is undefined
      };
    });

    return routineHazards;
  }


  @get('/non-routine-list')
  @response(200, {
    description: 'Array of HighRiskHazard model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {$ref: '#/components/schemas/HighRiskHazard'},
        },
      },
    },
  })
  async findNonRoutineList(
    @param.filter(RiskAssessment) filter?: Filter<RiskAssessment>,
  ): Promise<HighRiskHazard[]> {
    // Fetch data including the related workActivity
    const riskAssessments = await this.riskAssessmentRepository.find({
      ...filter,
      where: {
        ...filter?.where,
        type: 'Non Routine',
        status: 'Published',
      },
      include: [{relation: 'workActivity'}],
    });

    // Map the data to match HighRiskHazard structure
    const routineHazards: HighRiskHazard[] = riskAssessments.map((record: RiskAssessment) => {
      // Use the workActivity relation to get the hazard name, or fallback to 'Unknown Hazard'
      const hazardName = record?.nonRoutineWorkActivity ?? 'Unknown Hazard';

      // Extract only current_control options from tasks
      const controls: ControlOption[] = (record.tasks || [])
        .flatMap((taskArray: {type: string; option: ControlOption[]}[]) =>
          taskArray.filter((task: any) => task.type === 'current_control')
        )
        .flatMap((task: any) => task.option);

      return {
        hazardName: hazardName,
        controls: controls,
        riskAssessmentId: record?.id ?? '', // Fallback if id is undefined
      };
    });

    return routineHazards;
  }


  @get('/risk-assessments/{id}')
  @response(200, {
    description: 'RiskAssessment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskAssessment, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(RiskAssessment, {exclude: 'where'}) filter?: FilterExcludingWhere<RiskAssessment>
  ): Promise<RiskAssessment> {
    return this.riskAssessmentRepository.findById(id, filter);
  }


  @put('/risk-assessments/{id}')
  @response(204, {
    description: 'RiskAssessment PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() riskAssessment: RiskAssessment,
  ): Promise<void> {
    await this.riskAssessmentRepository.replaceById(id, riskAssessment);
  }

  @del('/risk-assessments/{id}')
  @response(204, {
    description: 'RiskAssessment DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.actionRepository.updateAll({status: 'Deleted'}, {applicationId: id})
    await this.riskAssessmentRepository.updateById(id, {status: 'Deleted'});
  }
}
