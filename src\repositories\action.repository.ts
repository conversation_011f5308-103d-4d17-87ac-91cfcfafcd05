import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor} from '@loopback/repository';
import {Action, ActionRelations, Service, User} from '../models';
import {ServiceRepository} from './service.repository';
import {UserRepository} from './user.repository';

export class ActionRepository extends DefaultCrudRepository<
  Action,
  typeof Action.prototype.id,
  ActionRelations
> {

  public readonly service: BelongsToAccessor<Service, typeof Action.prototype.id>;

  public readonly submittedBy: BelongsToAccessor<User, typeof Action.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('ServiceRepository') protected serviceRepositoryGetter: Getter<ServiceRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(Action, dataSource);
    this.submittedBy = this.createBelongsToAccessorFor('submittedBy', userRepositoryGetter,);
    this.registerInclusionResolver('submittedBy', this.submittedBy.inclusionResolver);
    this.service = this.createBelongsToAccessorFor('service', serviceRepositoryGetter,);
    this.registerInclusionResolver('service', this.service.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }


}

