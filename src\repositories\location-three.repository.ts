import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {LocationFour, LocationThree, LocationThreeRelations} from '../models';
import {LocationFourRepository} from './location-four.repository';

export class LocationThreeRepository extends DefaultCrudRepository<
  LocationThree,
  typeof LocationThree.prototype.id,
  LocationThreeRelations
> {

  public readonly locationFours: HasManyRepositoryFactory<LocationFour, typeof LocationThree.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>,
  ) {
    super(LocationThree, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.locationFours = this.createHasManyRepositoryFactoryFor('locationFours', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFours', this.locationFours.inclusionResolver);
  }
}
