import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  requestBody,
  response
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, Investigation} from '../models';
import {ActionRepository, IncidentRepository, InvestigationRecommendationRepository, InvestigationRepository, ServiceRepository} from '../repositories';

const SERVICE_NAME = 'INCINV';

@authenticate('cognito-jwt')
export class InvestigationController {
  constructor(
    @repository(InvestigationRepository)
    public investigationRepository: InvestigationRepository,
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @repository(InvestigationRecommendationRepository)
    public investigationRecommendationRepository: InvestigationRecommendationRepository,
  ) { }

  @post('/investigations')
  @response(200, {
    description: 'Investigation model instance',
    content: {'application/json': {schema: getModelSchemaRef(Investigation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {
            title: 'NewInvestigation',
            exclude: ['id'],
          }),
        },
      },
    })
    investigation: Omit<Investigation, 'id'>,
  ): Promise<Investigation> {
    return this.investigationRepository.create(investigation);
  }

  @get('/investigations/count')
  @response(200, {
    description: 'Investigation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Investigation) where?: Where<Investigation>,
  ): Promise<Count> {
    return this.investigationRepository.count(where);
  }

  @get('/investigations')
  @response(200, {
    description: 'Array of Investigation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Investigation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Investigation) filter?: Filter<Investigation>,
  ): Promise<Investigation[]> {
    const investigationData = await this.investigationRepository.find(filter);

    const modifiedInvestigation = await Promise.all(
      investigationData.map(async (data) => {

        // Then fetch actions for each audit finding and include the auditFindings data in each action

        const totalActions = await this.actionRepository.find({where: {applicationId: data.id}});
        // Map each action to include the auditFinding data under applicationDetails


        // Filter actions to find those that are completed
        const completedActions = totalActions.filter(action => action.status === 'Completed');

        // Create an instance of ReportIncident with the desired properties
        const modifiedReport = new Investigation({
          ...data,

          totalActions: totalActions,
          completedActions: completedActions

        });

        return modifiedReport;
      })
    );

    return modifiedInvestigation;
  }

  @patch('/investigations')
  @response(200, {
    description: 'Investigation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {partial: true}),
        },
      },
    })
    investigation: Investigation,
    @param.where(Investigation) where?: Where<Investigation>,
  ): Promise<Count> {
    return this.investigationRepository.updateAll(investigation, where);
  }

  @get('/investigations/{id}')
  @response(200, {
    description: 'Investigation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Investigation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Investigation, {exclude: 'where'}) filter?: FilterExcludingWhere<Investigation>
  ): Promise<Investigation> {
    return this.investigationRepository.findById(id, filter);
  }

  @patch('/investigations/{id}')
  @response(204, {
    description: 'Investigation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {partial: true}),
        },
      },
    })
    investigation: Investigation,
  ): Promise<void> {
    await this.investigationRepository.updateById(id, investigation);
  }

  @patch('/submit-investigations-for-approval/{actionId}')
  @response(204, {
    description: 'Investigation PATCH success',
  })
  async submitForApprovalById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {partial: true}),
        },
      },
    })
    investigation: Investigation,
  ): Promise<void> {
    const actionData = await this.actionRepository.findById(actionId)
    const investigationData = await this.investigationRepository.findById(actionData.objectId)
    const incidentData = await this.incidentRepository.findById(investigationData.incidentId)
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    if (!incidentData) {
      throw new Error('Incident Not Found!')
    }
    const actions: Partial<Action> =
    {
      application: SERVICE_NAME,
      actionType: 'approve_investigation',
      actionToBeTaken: 'Verify Investigation',
      description: incidentData.description,
      maskId: incidentData.maskId,
      trackId: uuidv4(), // Generate unique id
      sequence: '1',
      prefix: 'INCINV',
      applicationId: incidentData.id,
      dueDate: '',
      objectId: investigationData.id,
      submittedById: currentUserProfile[securityId],
      assignedToId: [investigation.investigationApproverId],
      submitURL: '/submit-investigations',
      status: 'Initiated',
      serviceId: service.id,

    };


    // Insert into actionRepository
    await this.actionRepository.create(actions);
    await this.actionRepository.updateById(actionId, {status: 'Completed'})
    await this.investigationRepository.updateById(actionData.objectId, {investigationApproverId: investigation.investigationApproverId, status: 'Pending Verification'});

  }

  @patch('/submit-investigations/{actionId}')
  @response(204, {
    description: 'Investigation PATCH success',
  })
  async submitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {partial: true}),
        },
      },
    })
    investigation: Investigation,
  ): Promise<void> {

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const incidentData = await this.incidentRepository.findById(actionData.applicationId)
    const investigationData = await this.investigationRepository.findById(actionData.objectId)


    if (!investigationData) {
      throw new Error(`Investigation data not found for ID: ${actionData.objectId}`);
    }

    switch (actionData.actionType) {
      case 'reconduct_investigation':
        {
          const actions: Partial<Action> =
          {
            application: SERVICE_NAME,
            actionType: 'approve_investigation',
            actionToBeTaken: 'Verify Investigation',
            description: incidentData.description,
            maskId: incidentData.maskId,
            trackId: actionData.trackId, // Generate unique id
            sequence: '1',
            prefix: 'INCINV',
            applicationId: actionData.applicationId,
            dueDate: '',
            objectId: actionData.objectId,
            submittedById: currentUserProfile[securityId],
            assignedToId: [investigationData.investigationApproverId],
            submitURL: '/submit-investigations',
            status: 'Initiated',
            serviceId: service.id,

          };


          // Insert into actionRepository
          await this.actionRepository.create(actions);
          await this.investigationRepository.updateById(investigationData.id, {status: 'Pending Verification'});
          break;
        }
      case 'approve_investigation':
        {

          if (investigation.status !== 'Returned' && investigation.status !== 'Completed') {
            throw new Error('Method Type Status not allowed')
          }

          switch (investigation.status) {
            case 'Returned':
              {
                const actions: Partial<Action> =
                {
                  application: SERVICE_NAME,
                  actionType: 'reconduct_investigation',
                  actionToBeTaken: 'Reconduct Investigation',
                  description: incidentData.description,
                  maskId: incidentData.maskId,
                  trackId: actionData.trackId, // Generate unique id
                  sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
                  prefix: 'INCINV',
                  applicationId: actionData.applicationId,
                  dueDate: '',
                  objectId: actionData.objectId,
                  submittedById: currentUserProfile[securityId],
                  assignedToId: [investigationData.investigatorId],
                  submitURL: '/submit-investigations',
                  status: 'Initiated',
                  serviceId: service.id,
                  remarks: investigation.investigationApproverRemarks,
                  comments: investigation.investigationApproverRemarks

                };


                // Insert into actionRepository
                await this.actionRepository.create(actions);
                await this.investigationRepository.updateById(investigationData.id, {status: 'Pending Verification', investigationApproverRemarks: investigation.investigationApproverRemarks ?? ''});
                break;
              }
            case 'Completed':
              {
                await this.investigationRepository.updateById(investigationData.id, {status: 'Completed'});
                await this.incidentRepository.updateById(investigationData.incidentId, {status: 'Investigation Completed'})
                break;
              }

            default: throw new Error('Method Type Status not allowed')
          }

          break;
        }
    }
    await this.actionRepository.updateById(actionId, {status: 'Completed'})

  }



  @del('/investigations/{id}')
  @response(204, {
    description: 'Investigation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.investigationRepository.deleteById(id);
  }
}
