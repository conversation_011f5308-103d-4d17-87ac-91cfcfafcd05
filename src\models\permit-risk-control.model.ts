import {Entity, model, property} from '@loopback/repository';

@model()
export class PermitRiskControl extends Entity {
  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  evidence?: string[];

  @property({
    type: 'string',
  })
  value?: 'Yes' | 'No' | 'N/A';

  @property({
    type: 'string',
  })
  currentType?: string;

  @property({
    type: 'boolean',
  })
  required?: boolean;

  @property({
    type: 'boolean',
  })
  validity?: boolean;

  @property({
    type: 'array',
    itemType: 'string',
  })
  files?: string[];

  @property({
    type: 'string',
  })
  method?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  permitType?: string;

  @property({
    type: 'date',
  })
  startDate?: string;

  @property({
    type: 'date',
  })
  endDate?: string;


  constructor(data?: Partial<PermitRiskControl>) {
    super(data);
  }
}

export interface PermitRiskControlRelations {
  // describe navigational properties here
}

export type PermitRiskControlWithRelations = PermitRiskControl & PermitRiskControlRelations;
