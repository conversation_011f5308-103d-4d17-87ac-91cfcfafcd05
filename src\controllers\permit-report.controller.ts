import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import moment from 'moment';
import {v4 as uuidv4} from 'uuid';
import {Action, PermitReport, PermitRoleStatus} from '../models';
import {ActionRepository, PermitReportRepository, RiskAssessmentRepository, ServiceRepository, UserRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
const SERVICE_NAME = 'EPTW-GEN';
const MASK_NAME = 'EPTW';

@authenticate('cognito-jwt')
export class PermitReportController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/permit-reports')
  @response(200, {
    description: 'PermitReport model instance',
    content: {'application/json': {schema: getModelSchemaRef(PermitReport)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReport',
            exclude: ['id'],
          }),
        },
      },
    })
    permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {
    const userId = currentUserProfile[securityId];
    permitReport.applicantId = userId;
    if (permitReport.applicantStatus) {
      permitReport.applicantStatus.status = true
      permitReport.applicantStatus.signedDate = new Date().toISOString();
    }

    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.permitReportRepository.count();
    const prefix = MASK_NAME;

    permitReport.maskId = `${prefix}-${year}${month}${day}-${count.count + 1}`;
    permitReport.status = 'Pending Review';
    const permitTypeArray = permitReport.permitType;
    if (!Array.isArray(permitTypeArray) || permitTypeArray.length === 0) {
      throw new Error('permitType is required and must be a non-empty array');
    }
    const permitTypeName = permitTypeArray[0];

    // Step 2: Find riskAssessment by name
    const riskAssessment = await this.riskAssessmentRepository.findOne({
      where: {hazardName: permitTypeName},
    });
    if (!riskAssessment) {
      throw new Error(`No riskAssessment found for name: ${permitTypeName}`);
    }

    // Step 3: Set riskAssessmentId in the permit
    permitReport.riskAssessmentId = riskAssessment?.id || '';

    // Step 4: Count existing permits with same riskAssessmentId
    const {count: riskCount} = await this.permitReportRepository.count({
      riskAssessmentId: riskAssessment.id,
    });

    // Step 5: Generate workOrderNumber
    permitReport.workOrderNo = `${riskAssessment.shortName}-${riskCount + 1}`;
    const ptwData = await this.permitReportRepository.create(permitReport);
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('No service found!')
    }

    if (permitReport.assessorId) {

      {
        const action: Partial<Action> = {

          application: SERVICE_NAME,
          actionType: 'Assess',
          actionToBeTaken: 'Assess ePTW Application',
          description: permitReport.workDescription,
          maskId: permitReport.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'EPTW',
          applicationId: ptwData.id,
          objectId: ptwData.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [permitReport.assessorId],
          submitURL: '/permit-report-submit',
          status: 'Initiated',
          serviceId: service.id,


        }


        await this.actionRepository.create(action)
        await this.permitReportRepository.updateById(ptwData.id, {assessorId: permitReport.assessorId, status: 'Pending Assessment'})
        if (ptwData.id)
          await this.generatePermitReportEmail(ptwData.id, `Assess ePTW Application: ${ptwData.maskId}`, permitReport.assessorId);
      }
      return ptwData;
    } else {
      const action: Partial<Action> = {

        application: SERVICE_NAME,
        actionType: 'Review',
        actionToBeTaken: 'Review ePTW Application',
        description: permitReport.workDescription,
        maskId: permitReport.maskId,
        trackId: uuidv4(), // Generate unique id
        sequence: '1',
        prefix: 'EPTW',
        applicationId: ptwData.id,
        objectId: ptwData.id,
        submittedById: currentUserProfile[securityId],
        assignedToId: [permitReport.reviewerId],
        submitURL: '/permit-report-submit',
        status: 'Initiated',
        serviceId: service.id

      }

      await this.actionRepository.create(action)
      if (ptwData.id)
        await this.generatePermitReportEmail(ptwData.id, `Review ePTW Application: ${ptwData.maskId}`, permitReport.reviewerId);
      return ptwData;
    }

  }

  @patch('/permit-report-submit/{actionId}')
  @response(204, {
    description: 'PTW PATCH success',
  })
  async submitByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})
    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const ptwData = await this.permitReportRepository.findById(actionData.objectId)
    if (!ptwData) {
      throw new Error('No OTT Data Found')
    }

    if (permitReport.status === 'Returned') {

      ptwData.applicantStatus = ({} as PermitRoleStatus);
      ptwData.reviewerStatus = ({} as PermitRoleStatus);
      ptwData.assessorStatus = ({} as PermitRoleStatus);
      ptwData.approverStatus = ({} as PermitRoleStatus);



      switch (actionData.actionType) {
        case 'Review':
          {
            // Initialize `reviewerStatus` if it doesn't exist


            // Set `comments` on `reviewerStatus`
            ptwData.reviewerStatus.comments = permitReport.comments;

          }
          break;
        case 'Assess':
          {
            // Initialize `reviewerStatus` if it doesn't exist


            // Set `comments` on `reviewerStatus`
            ptwData.assessorStatus.comments = permitReport.comments;

          }
          break;
        case 'Approve':
          {
            // Initialize `reviewerStatus` if it doesn't exist


            // Set `comments` on `reviewerStatus`
            ptwData.approverStatus.comments = permitReport.comments;

          }
          break;
      }

      await this.permitReportRepository.updateById(ptwData.id, {reviewerId: '', assessorId: '', approverId: '', applicantStatus: ptwData.applicantStatus, reviewerStatus: ptwData.reviewerStatus, assessorStatus: ptwData.assessorStatus, approverStatus: ptwData.approverStatus})

      const action: Partial<Action> = {

        application: SERVICE_NAME,
        actionType: 'Reapply',
        actionToBeTaken: 'Reapply ePTW Application',
        description: ptwData.workDescription,
        maskId: ptwData.maskId,
        trackId: actionData.trackId, // Generate unique id
        sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
        prefix: 'EPTW',
        applicationId: ptwData.id,
        comments: permitReport.comments,
        remarks: permitReport.comments,
        objectId: ptwData.id,
        submittedById: currentUserProfile[securityId],
        assignedToId: [ptwData.applicantId],
        submitURL: '/permit-report-submit',
        status: 'Initiated',
        serviceId: service.id

      }

      await this.actionRepository.updateById(actionData.id, {comments: permitReport.comments, remarks: permitReport.comments, status: 'Completed'})
      await this.actionRepository.create(action)
      await this.permitReportRepository.updateById(ptwData.id, {comments: permitReport.comments ?? '', status: 'Returned'})
      if (ptwData.id)
        await this.generatePermitReportEmail(ptwData.id, `ePTW Application Returned: ${ptwData.maskId}`, ptwData.applicantId, permitReport.comments);
      return;
    }

    switch (actionData.actionType) {
      case 'Review':
        {
          const action: Partial<Action> = {

            application: SERVICE_NAME,
            actionType: 'Assess',
            actionToBeTaken: 'Assess ePTW Application',
            description: ptwData.workDescription,
            maskId: ptwData.maskId,
            trackId: actionData.trackId, // Generate unique id
            sequence: actionData.sequence,
            prefix: 'EPTW',
            applicationId: ptwData.id,
            objectId: ptwData.id,
            submittedById: currentUserProfile[securityId],
            assignedToId: [permitReport.assessorId],
            submitURL: '/permit-report-submit',
            status: 'Initiated',
            serviceId: service.id,
            comments: permitReport.comments

          }

          if (permitReport.reviewerStatus) {
            permitReport.reviewerStatus.status = true
            permitReport.reviewerStatus.signedDate = new Date().toISOString();
            permitReport.reviewerStatus.comments = permitReport.comments;
          }
          await this.actionRepository.create(action)
          await this.permitReportRepository.updateById(ptwData.id, {assessorId: permitReport.assessorId, reviewerStatus: permitReport.reviewerStatus, status: 'Pending Assessment', comments: permitReport.comments})
          if (ptwData.id)
            await this.generatePermitReportEmail(ptwData.id, `Assess ePTW Application: ${ptwData.maskId}`, permitReport.assessorId, permitReport.comments);
        }
        break;

      case 'Assess':
        {
          const action: Partial<Action> = {

            application: SERVICE_NAME,
            actionType: 'Approve',
            actionToBeTaken: 'Approve ePTW Application',
            description: ptwData.workDescription,
            maskId: ptwData.maskId,
            trackId: actionData.trackId, // Generate unique id
            sequence: actionData.sequence,
            prefix: 'EPTW',
            applicationId: ptwData.id,
            objectId: ptwData.id,
            submittedById: currentUserProfile[securityId],
            assignedToId: [permitReport.approverId],
            submitURL: '/permit-report-submit',
            status: 'Initiated',
            serviceId: service.id,
            comments: permitReport.comments

          }

          if (permitReport.assessorStatus) {
            permitReport.assessorStatus.status = true
            permitReport.assessorStatus.signedDate = new Date().toISOString();
            permitReport.assessorStatus.comments = permitReport.comments;
          }
          await this.actionRepository.create(action)
          await this.permitReportRepository.updateById(ptwData.id, {approverId: permitReport.approverId, assessorStatus: permitReport.assessorStatus, status: 'Pending Approval', comments: permitReport.comments})
          if (ptwData.id)
            await this.generatePermitReportEmail(ptwData.id, `Verify ePTW Application: ${ptwData.maskId}`, permitReport.approverId, permitReport.comments);
        }
        break;

      case 'Approve':
        {


          if (permitReport.approverStatus) {
            permitReport.approverStatus.status = true
            permitReport.approverStatus.signedDate = new Date().toISOString();
            permitReport.approverStatus.comments = permitReport.comments;
          }

          await this.permitReportRepository.updateById(ptwData.id, {approverStatus: permitReport.approverStatus, status: 'Active', comments: permitReport.comments})
          if (ptwData.id)
            await this.generatePermitReportEmail(ptwData.id, `ePTW Application is Approved and Active: ${ptwData.maskId}`, ptwData.applicantId, permitReport.comments);
        }
        break;

      case 'Reapply':
        {
          const action: Partial<Action> = {

            application: SERVICE_NAME,
            actionType: 'Review',
            actionToBeTaken: 'Review ePTW Application',
            description: permitReport.workDescription,
            maskId: permitReport.maskId,
            trackId: actionData.trackId, // Generate unique id
            sequence: actionData.sequence,
            prefix: 'EPTW',
            applicationId: ptwData.id,
            objectId: ptwData.id,
            submittedById: currentUserProfile[securityId],
            assignedToId: [permitReport.reviewerId],
            submitURL: '/permit-report-submit',
            status: 'Initiated',
            serviceId: service.id

          }

          if (permitReport.applicantStatus) {
            permitReport.applicantStatus.status = true
            permitReport.applicantStatus.signedDate = new Date().toISOString();
          }
          await this.actionRepository.create(action)
          await this.permitReportRepository.updateById(ptwData.id, {...permitReport, status: 'Pending Review'})
          if (ptwData.id)
            await this.generatePermitReportEmail(ptwData.id, `Review ePTW Application : ${ptwData.maskId}`, permitReport.reviewerId, permitReport.comments);
        }
        break;
    }

    await this.actionRepository.updateById(actionId, {comments: permitReport.comments ?? '', remarks: permitReport.comments ?? '', status: 'Completed'})


  }

  @get('/permit-reports/count')
  @response(200, {
    description: 'PermitReport model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.count(where);
  }

  @get('/permit-reports')
  @response(200, {
    description: 'Array of PermitReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {


    const finalFilter: Filter<PermitReport> = {
      ...filter,
      order: filter?.order ?? ['created DESC'],
      limit: 150, // Limit results to top 150
    };

    const allReports = await this.permitReportRepository.find(finalFilter);

    const archivedStatuses = ['Acknowledged & Closed', 'Withdrawn', 'Return', 'Closed'];
    const sevenDaysAgo = moment().subtract(7, 'days');

    const filteredReports = allReports.filter(report => {
      if (!archivedStatuses.includes(report.status ?? '')) {
        return true;
      }

      const permitEnd = moment(report.permitEndDate);
      return permitEnd.isAfter(sevenDaysAgo);
    });

    return filteredReports.slice(0, 150);
  }

  @get('/archived-permits')
  @response(200, {
    description: 'Paginated archived PermitReports from the last 7 days with default filter support',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: getModelSchemaRef(PermitReport, {includeRelations: true}),
            },
            total: {type: 'number'},
            page: {type: 'number'},
            limit: {type: 'number'},
          },
        },
      },
    },
  })
  async getArchivedPermits(
    @param.query.number('page') page: number = 1,
    @param.query.number('limit') limit: number = 20,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<{data: PermitReport[]; total: number; page: number; limit: number}> {
    const archivedStatuses = ['Acknowledged & Closed', 'Withdrawn', 'Return', 'Closed'] as PermitReport['status'][];

    const skip = (page - 1) * limit;

    // Merge incoming filter with custom logic
    const finalFilter: Filter<PermitReport> = {
      ...filter,
      where: {
        and: [
          {status: {inq: archivedStatuses}},
          ...(filter?.where ? [filter.where] : []),
        ],
      },
      order: filter?.order ?? ['permitEndDate DESC'],
      skip,
      limit,
    };

    const [data, total] = await Promise.all([
      this.permitReportRepository.find(finalFilter),
      this.permitReportRepository.count(finalFilter.where).then(res => res.count),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  @patch('/permit-reports')
  @response(200, {
    description: 'PermitReport PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: PermitReport,
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.updateAll(permitReport, where);
  }

  @get('/permit-reports/{id}')
  @response(200, {
    description: 'PermitReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(PermitReport, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PermitReport, {exclude: 'where'}) filter?: FilterExcludingWhere<PermitReport>
  ): Promise<PermitReport> {
    return this.permitReportRepository.findById(id, filter);
  }

  @patch('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-update-status/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateStatusById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {


    const userId = currentUserProfile[securityId];


    const userData = await this.userRepository.findById(userId);


    const permitData = await this.permitReportRepository.findById(id);


    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}});
    if (!service) {
      console.error('Service not found');
      throw new Error('Service not found');
    }


    if (permitReport.status === 'Closed' || permitReport.status === 'Suspended' || permitReport.status === 'Withdrawn') {


      if (permitReport.status === 'Suspended') {

        const action: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'Closeout',
          actionToBeTaken: 'Closeout ePTW Application',
          description: permitData.workDescription,
          maskId: permitData.maskId,
          trackId: '', // Generate unique id
          sequence: '1',
          prefix: 'EPTW',
          applicationId: id,
          objectId: id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [permitData.applicantId],
          submitURL: '/permit-reports-update-status',
          status: 'Initiated',
          serviceId: service.id,
        };
        await this.actionRepository.create(action);
        if (permitData.id)
          await this.generatePermitReportEmail(permitData.id, `Closeout ePTW Application: ${permitData.maskId}`, permitData.applicantId);
      }

      if (permitReport.status === 'Closed') {
        //trigger acknowledge action card



        if (permitReport.closeoutStatus) {
          permitReport.closeoutStatus.by = userData.firstName;
          permitReport.closeoutStatus.byEmail = userData.email;
          permitReport.closeoutStatus.status = permitReport.status;
          permitReport.closeoutStatus.signedDate = new Date().toISOString();
          console.log('Closeout status updated:', permitReport.closeoutStatus);

        }
      } else {

        if (permitReport.permitStatus) {
          permitReport.permitStatus.by = userData.firstName;
          permitReport.permitStatus.byEmail = userData.email;
          permitReport.permitStatus.status = permitReport.status;
          permitReport.permitStatus.signedDate = new Date().toISOString();
          console.log('Permit status updated:', permitReport.permitStatus);
        }
      }


      await this.permitReportRepository.updateById(id, {remarks: permitReport.remarks, status: permitReport.status, permitStatus: permitReport.permitStatus ?? {}, closeoutStatus: permitReport.closeoutStatus ?? {}});

      if (permitReport.status !== 'Suspended') {
        await this.actionRepository.updateAll({status: 'Completed'}, {objectId: id});
      }

      if (permitReport.status === 'Closed') {
        const action: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'Acknowledgement',
          actionToBeTaken: 'Acknowledge ePTW Application',
          description: permitData.workDescription,
          maskId: permitData.maskId,
          trackId: '', // Generate unique id
          sequence: '1',
          prefix: 'EPTW',
          applicationId: permitData.id,
          objectId: permitData.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [permitReport.acknowledgerId],
          submitURL: '/permit-reports-update-status',
          status: 'Initiated',
          serviceId: service.id,
        };
        await this.actionRepository.create(action);
        if (permitData.id)
          await this.generatePermitReportEmail(permitData.id, `Acknowledge ePTW Application: ${permitData.maskId}`, permitReport.acknowledgerId);
      }



    }
  }

  @patch('/permit-reports-closeout/{actionId}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateCloseoutStatusById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {

    const userId = currentUserProfile[securityId];
    const userData = await this.userRepository.findById(userId);
    const actionData = await this.actionRepository.findById(actionId)
    const permitData = await this.permitReportRepository.findById(actionData.objectId);
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}});
    if (!service) {
      console.error('Service not found');
      throw new Error('Service not found');
    }

    if (permitReport.closeoutStatus) {
      permitReport.closeoutStatus.by = userData.firstName;
      permitReport.closeoutStatus.byEmail = userData.email;
      permitReport.closeoutStatus.status = permitReport.status;
      permitReport.closeoutStatus.signedDate = new Date().toISOString();

      permitReport.remarks = permitReport.closeoutStatus.comments ?? '';
    }
    await this.permitReportRepository.updateById(actionData.applicationId, {remarks: permitReport.remarks, status: 'Suspended & Closed', closeoutStatus: permitReport.closeoutStatus ?? {}});
    await this.actionRepository.updateById(actionId, {status: 'Completed'});
    const action: Partial<Action> = {
      application: SERVICE_NAME,
      actionType: 'Acknowledgement',
      actionToBeTaken: 'Acknowledge ePTW Application',
      description: permitData.workDescription,
      maskId: permitData.maskId,
      trackId: '', // Generate unique id
      sequence: '1',
      prefix: 'EPTW',
      applicationId: permitData.id,
      objectId: permitData.id,
      submittedById: currentUserProfile[securityId],
      assignedToId: [permitReport.acknowledgerId],
      submitURL: '/permit-reports-update-status',
      status: 'Initiated',
      serviceId: service.id,
    };
    await this.actionRepository.create(action);
    if (permitData.id)
      await this.generatePermitReportEmail(permitData.id, `Acknowledge ePTW Application: ${permitData.maskId}`, permitReport.acknowledgerId);
  }

  @patch('/permit-reports-acknowledge/{actionId}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateAcknowledgeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {partial: true}),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const userId = currentUserProfile[securityId];
    const userData = await this.userRepository.findById(userId);
    const actionData = await this.actionRepository.findById(actionId)
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}});
    if (!service) {
      console.error('Service not found');
      throw new Error('Service not found');
    }

    if (permitReport.acknowledgementStatus) {
      permitReport.acknowledgementStatus.by = userData.firstName;
      permitReport.acknowledgementStatus.byEmail = userData.email;
      permitReport.acknowledgementStatus.status = 'Acknowledged & Closed';
      permitReport.acknowledgementStatus.signedDate = new Date().toISOString();

      permitReport.remarks = permitReport.acknowledgementStatus.comments ?? '';
    }

    await this.permitReportRepository.updateById(actionData.applicationId, {acknowledgementStatus: permitReport.acknowledgementStatus, status: 'Acknowledged & Closed', });
    await this.actionRepository.updateById(actionId, {status: 'Completed'});

  }

  @put('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() permitReport: PermitReport,
  ): Promise<void> {
    await this.permitReportRepository.replaceById(id, permitReport);
  }

  @del('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.permitReportRepository.deleteById(id);
  }

  private async generatePermitReportEmail(
    id: string,
    subject: string,
    to: string,
    comments?: string
  ): Promise<void> {
    // Fetch the permit report by ID
    const toUser = await this.userRepository.findById(to);
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        {relation: 'locationOne'},
        {relation: 'locationTwo'},
        {relation: 'locationThree'},
        {relation: 'locationFour'},
        {relation: 'locationFive'},
        {relation: 'locationSix'},
        {relation: 'applicant'}, // Fetch user info like firstName and email
      ],
    });

    // Extract locations dynamically and join with ">"
    const locationHierarchy = [
      permitData.locationOne?.name,
      permitData.locationTwo?.name,
      permitData.locationThree?.name,
      permitData.locationFour?.name,
      permitData.locationFive?.name,
      permitData.locationSix?.name,
    ]
      .filter(location => location) // Remove undefined or null locations
      .join(' > ');

    // Extract and format permit types
    const typeOfPermit = permitData?.permitType?.join(', ');

    // Extract permit duration
    const permitDuration = `${new Date(
      permitData?.permitStartDate ?? ''
    ).toLocaleString()} to ${new Date(
      permitData?.permitEndDate ?? ''
    ).toLocaleString()}`;

    // Extract other fields
    const descriptionOfWork = permitData.workDescription;
    const supervisorName = permitData.nameOfSiteSupervisor;
    const supervisorContact = permitData.supervisorContactNo;
    const applicantContact = permitData.applicantContactNo;

    // Extract user data
    const user = permitData.applicant; // Assuming user relation contains email and firstName
    const userFirstName = user?.firstName ?? 'User';
    const userCompanyName = user?.company ?? 'N/A';

    // Construct email body
    const mailBody = `
    <html lang="en">
    <body>
        <p><strong>ID:</strong> ${permitData.maskId}</p>
        <p><strong>Name of Applicant and Company Name:</strong> ${userFirstName} - ${userCompanyName} <strong>Contact:</strong> ${applicantContact}</p>
        <p><strong>Location:</strong> ${locationHierarchy || 'N/A'}</p>
        <p><strong>Type of Permit:</strong> ${typeOfPermit}</p>
        <p><strong>Permit Duration:</strong> ${permitDuration}</p>
        <p><strong>Description of Work:</strong> ${descriptionOfWork}</p>
        <p><strong>Supervisor:</strong> ${supervisorName}, <strong>Contact:</strong> ${supervisorContact}</p>
        ${comments ? `<p><strong>Comments:</strong> ${comments}</p>` : ''}
    </body>
    </html>`;

    if (toUser) {
      await this.sqsService.sendMessage(toUser, subject, mailBody);
    }

  }





}






