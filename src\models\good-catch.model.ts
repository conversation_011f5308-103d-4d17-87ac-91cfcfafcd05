import {Entity, model, property, belongsTo, hasMany} from '@loopback/repository';
import {User} from './user.model';
import {Action} from './action.model';
import {WorkingGroup} from './working-group.model';
import {LocationOne} from './location-one.model';
import {LocationTwo} from './location-two.model';
import {LocationThree} from './location-three.model';
import {LocationFour} from './location-four.model';
import {LocationFive} from './location-five.model';
import {LocationSix} from './location-six.model';

@model()
export class GoodCatch extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  whatDidYouObserve?: string;

  @property({
    type: 'string',
  })
  whereDidYouObserve?: string;

  @property({
    type: 'string',
  })
  whatCouldHaveGoneWrong?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  department?: string;

  @property({
    type: 'string',
  })
  preventiveAction?: string;

  @property({
    type: 'string',
  })
  workActivity?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  actionOwnerUploads?: string[];

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'date',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  immediateActionTaken?: string;

  @property({
    type: 'date',
  })
  immediateActionDate?: string;

  @property({
    type: 'string',
  })
  importanceRating?: string;

  @property({
    type: 'date',
  })
  closureDateTime?: string;

  @belongsTo(() => User)
  reporterId: string;

  @belongsTo(() => User)
  adminId: string;

  @belongsTo(() => User)
  actionOwnerId: string;

  @hasMany(() => Action)
  actions: Action[];

  @belongsTo(() => WorkingGroup)
  workingGroupId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  constructor(data?: Partial<GoodCatch>) {
    super(data);
  }
}

export interface GoodCatchRelations {
  // describe navigational properties here
}

export type GoodCatchWithRelations = GoodCatch & GoodCatchRelations;
