import {Entity, model, property} from '@loopback/repository';

@model()
export class DocumentCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  constructor(data?: Partial<DocumentCategory>) {
    super(data);
  }
}

export interface DocumentCategoryRelations {
  // describe navigational properties here
}

export type DocumentCategoryWithRelations = DocumentCategory & DocumentCategoryRelations;
