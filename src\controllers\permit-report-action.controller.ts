import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  PermitReport,
  Action,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportActionController {
  constructor(
    @repository(PermitReportRepository) protected permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'Array of PermitReport has many Action',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Action)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.permitReportRepository.permitReportActions(id).find(filter);
  }

  @post('/permit-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'PermitReport model instance',
        content: {'application/json': {schema: getModelSchemaRef(Action)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewActionInPermitReport',
            exclude: ['id'],
            optional: ['objectId']
          }),
        },
      },
    }) action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.permitReportRepository.permitReportActions(id).create(action);
  }

  @patch('/permit-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'PermitReport.Action PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Partial<Action>,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.permitReportRepository.permitReportActions(id).patch(action, where);
  }

  @del('/permit-reports/{id}/actions', {
    responses: {
      '200': {
        description: 'PermitReport.Action DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Action)) where?: Where<Action>,
  ): Promise<Count> {
    return this.permitReportRepository.permitReportActions(id).delete(where);
  }
}
