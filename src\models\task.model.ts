import {Entity, model, property, belongsTo} from '@loopback/repository';
import {User} from './user.model';

@model()
export class Task extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  /**
   * The type/kind of task
   * e.g. "perform_task", "verify_task", "approve", "reject", "investigate"
   */
  @property({
    type: 'string',
    required: true,
  })
  taskType: string;

  /**
   * The current status of this task
   * e.g. "Initiated", "In Progress", "Completed", "Returned", "On Hold"
   */
  @property({
    type: 'string',
    required: true,
  })
  status: string;


  /**
   * A short description or instructions for this task
   */
  @property({
    type: 'string',
  })
  description?: string;

  /**
   * The date/time by which this task should be completed
   */
  @property({
    type: 'date',
  })
  dueDate?: Date;

  /**
   * The application/module this task belongs to
   * e.g. "OTT", "INCIDENT", "OBSERVATION", etc.
   * If you have multiple domains sharing one Task table
   */
  @property({
    type: 'string',
  })
  application?: string;

  /**
   * The ID of the domain object (record) this task is linked to
   * e.g. 'Ott' record ID, 'Incident' record ID
   */
  @property({
    type: 'string',
  })
  objectId?: string;

  /**
   * A user-friendly "mask" or reference ID for the domain record
   * e.g. "OTT-20250101-001"
   */
  @property({
    type: 'string',
  })
  maskId?: string;

  /**
   * A workflow or process ID that can link multiple related tasks
   * e.g. multiple steps in the same sequence
   */
  @property({
    type: 'string',
  })
  trackId?: string;

  /**
   * An optional numeric or string-based sequence
   * e.g. "1", "2", "3" to show task ordering
   */
  @property({
    type: 'string',
  })
  sequence?: string;

  /**
   * A more human-readable label of what needs to be done
   * e.g. "Review Task Completion", "Perform Corrective Action"
   */
  @property({
    type: 'string',
  })
  taskToBeDone?: string;

  /**
   * Additional free-form comments or notes for this task
   */
  @property({
    type: 'string',
  })
  comments?: string;

  /**
   * Priority or urgency of this task
   * e.g. "High", "Medium", "Low"
   */
  @property({
    type: 'string',
  })
  priority?: string;

  /**
   * A URL endpoint where this task should be "submitted" or updated
   * e.g. "/ott-task-submit"
   */
  @property({
    type: 'string',
  })
  submitURL?: string;

  /**
   * A direct URL/link to the relevant domain object/page
   * e.g. "/ott/123/view" for quick navigation
   */
  @property({
    type: 'string',
  })
  objectURL?: string;

  /**
   * If you want to store the "next" suggested taskType
   * in a more automated workflow scenario
   */
  @property({
    type: 'string',
  })
  nextTaskType?: string;

  /**
   * Timestamp when this task was created
   * (Optional; can also rely on createdAt in the DB)
   */
  @property({
    type: 'date',
    defaultFn: 'now', // Let LoopBack auto-set the creation date
  })
  createdAt?: Date;

  /**
   * Timestamp when this task was last updated
   * (Optional; can also rely on updatedAt triggers in the DB)
   */
  @property({
    type: 'date',
  })
  updatedAt?: Date;

  /**
   * Timestamp when the task was closed/completed
   * e.g. when status is changed to "Completed"
   */
  @property({
    type: 'date',
  })
  closedAt?: Date;

  /**
   * The user who closed or completed the task
   */
  @property({
    type: 'string',
  })
  closedById?: string;

  /**
   * Number of times this task was returned or reopened
   * Useful to track how many "returns" happened
   */
  @property({
    type: 'number',
    default: 0,
  })
  reopenCount?: number;

  @belongsTo(() => User)
  submittedById: string;

  @belongsTo(() => User)
  assignedToId: string;

  constructor(data?: Partial<Task>) {
    super(data);
  }
}

export interface TaskRelations {
  // describe navigational properties here
}

export type TaskWithRelations = Task & TaskRelations;
