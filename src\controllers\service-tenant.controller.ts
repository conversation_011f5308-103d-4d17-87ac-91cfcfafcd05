import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Service,
  Tenant
} from '../models';
import {ServiceRepository} from '../repositories';

// import {authenticate} from '@loopback/authentication';

// @authenticate('cognito-jwt')
export class ServiceTenantController {
  constructor(
    @repository(ServiceRepository) protected serviceRepository: ServiceRepository,
  ) { }

  @get('/services/{id}/tenants', {
    responses: {
      '200': {
        description: 'Array of Service has many Tenant through TenantService',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Tenant)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Tenant>,
  ): Promise<Tenant[]> {
    return this.serviceRepository.tenants(id).find(filter);
  }

  @post('/services/{id}/tenants', {
    responses: {
      '200': {
        description: 'create a Tenant model instance',
        content: {'application/json': {schema: getModelSchemaRef(Tenant)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Service.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Tenant, {
            title: 'NewTenantInService',
            exclude: ['id'],
          }),
        },
      },
    }) tenant: Omit<Tenant, 'id'>,
  ): Promise<Tenant> {
    return this.serviceRepository.tenants(id).create(tenant);
  }

  @patch('/services/{id}/tenants', {
    responses: {
      '200': {
        description: 'Service.Tenant PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Tenant, {partial: true}),
        },
      },
    })
    tenant: Partial<Tenant>,
    @param.query.object('where', getWhereSchemaFor(Tenant)) where?: Where<Tenant>,
  ): Promise<Count> {
    return this.serviceRepository.tenants(id).patch(tenant, where);
  }

  @del('/services/{id}/tenants', {
    responses: {
      '200': {
        description: 'Service.Tenant DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Tenant)) where?: Where<Tenant>,
  ): Promise<Count> {
    return this.serviceRepository.tenants(id).delete(where);
  }
}
