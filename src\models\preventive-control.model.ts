import {Entity, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {IncidentControl} from './incident-control.model';

@model()
export class PreventiveControl extends Entity {


  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(IncidentControl)

  })
  identifiedPreventiveControls?: IncidentControl[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(IncidentControl)

  })
  unIdentifiedPreventiveControls?: IncidentControl[];

  @property({
    type: 'boolean',
  })
  controlIdentified?: boolean;
  @property({
    type: 'boolean',
  })
  controlNotIdentified?: boolean;

  @property({
    type: 'boolean',
  })
  wasHazardControllable?: boolean;

  @property({
    type: 'string',
  })
  explainHazardControllable?: string;

  @property({
    type: 'string',
  })
  status?: "Draft" | "Completed";

  constructor(data?: Partial<PreventiveControl>) {
    super(data);
  }
}

export interface PreventiveControlRelations {
  // describe navigational properties here
}

export type PreventiveControlWithRelations = PreventiveControl & PreventiveControlRelations;
