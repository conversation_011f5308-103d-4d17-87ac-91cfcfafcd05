import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  LocationFourRepository,
  LocationOneRepository,
  LocationThreeRepository,
  LocationTwoRepository,
  UserLocationRoleRepository,
} from '../repositories';

@injectable({scope: BindingScope.TRANSIENT})
export class LocationFilterService {
  constructor(
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
  ) { }

  async getLocationFilterConditions(userId: string): Promise<any[]> {
    const userRoles = await this.userLocationRoleRepository.find({
      where: {userId},
    });

    let locationOneIds: string[] = [];
    let locationTwoIds: string[] = [];
    let locationThreeIds: string[] = [];
    let locationFourIds: string[] = [];

    await Promise.all(
      userRoles.map(async userLocations => {
        // Handle LocationOne (tier1-all)
        if (userLocations.locationOneId === 'tier1-all') {
          const allLocationOnes = await this.locationOneRepository.find();
          locationOneIds = locationOneIds.concat(
            allLocationOnes.map(loc => loc.id).filter((id): id is string => Boolean(id))
          );
        } else if (userLocations.locationOneId) {
          locationOneIds.push(userLocations.locationOneId);
        }

        // Handle LocationTwo (tier2-all)
        if (userLocations.locationTwoId === 'tier2-all') {
          const allLocationTwos = await this.locationTwoRepository.find({
            where: {locationOneId: userLocations.locationOneId},
          });
          locationTwoIds = locationTwoIds.concat(
            allLocationTwos.map(loc => loc.id).filter((id): id is string => Boolean(id))
          );
        } else if (userLocations.locationTwoId) {
          locationTwoIds.push(userLocations.locationTwoId);
        }

        // Handle LocationThree (tier3-all)
        if (userLocations.locationThreeId === 'tier3-all') {
          const allLocationThrees = await this.locationThreeRepository.find({
            where: {locationTwoId: userLocations.locationTwoId},
          });
          locationThreeIds = locationThreeIds.concat(
            allLocationThrees.map(loc => loc.id).filter((id): id is string => Boolean(id))
          );
        } else if (userLocations.locationThreeId) {
          locationThreeIds.push(userLocations.locationThreeId);
        }

        // Handle LocationFour (tier4-all)
        if (userLocations.locationFourId === 'tier4-all') {
          const allLocationFours = await this.locationFourRepository.find({
            where: {locationThreeId: userLocations.locationThreeId},
          });
          locationFourIds = locationFourIds.concat(
            allLocationFours.map(loc => loc.id).filter((id): id is string => Boolean(id))
          );
        } else if (userLocations.locationFourId) {
          locationFourIds.push(userLocations.locationFourId);
        }
      }),
    );

    // Build and return the OR filter conditions
    const conditions: any[] = [];
    if (locationOneIds.length > 0) {
      conditions.push({locationOneId: {inq: locationOneIds}});
    }
    if (locationTwoIds.length > 0) {
      conditions.push({locationTwoId: {inq: locationTwoIds}});
    }
    if (locationThreeIds.length > 0) {
      conditions.push({locationThreeId: {inq: locationThreeIds}});
    }
    if (locationFourIds.length > 0) {
      conditions.push({locationFourId: {inq: locationFourIds}});
    }

    return conditions;
  }
}
