import {Entity, model, property} from '@loopback/repository';

@model()
export class TenantService extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  tenantId?: string;

  @property({
    type: 'string',
  })
  serviceId?: string;

  constructor(data?: Partial<TenantService>) {
    super(data);
  }
}

export interface TenantServiceRelations {
  // describe navigational properties here
}

export type TenantServiceWithRelations = TenantService & TenantServiceRelations;
