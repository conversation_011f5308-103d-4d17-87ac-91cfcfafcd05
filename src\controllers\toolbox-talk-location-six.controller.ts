import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  LocationSix,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkLocationSixController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationSix),
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<LocationSix> {
    return this.toolboxTalkRepository.locationSix(id);
  }
}
