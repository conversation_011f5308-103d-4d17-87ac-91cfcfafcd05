import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Investigation,
  User,
} from '../models';
import {InvestigationRepository} from '../repositories';

export class InvestigationUserController {
  constructor(
    @repository(InvestigationRepository)
    public investigationRepository: InvestigationRepository,
  ) { }

  @get('/investigations/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Investigation',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Investigation.prototype.id,
  ): Promise<User> {
    return this.investigationRepository.investigator(id);
  }
}
