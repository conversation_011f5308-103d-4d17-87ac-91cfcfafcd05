import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  LocationFive,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportLocationFiveController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to PermitReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFive),
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<LocationFive> {
    return this.permitReportRepository.locationFive(id);
  }
}
