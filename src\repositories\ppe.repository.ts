import {inject} from '@loopback/core';
import {DefaultCrudRepository, juggler} from '@loopback/repository';
import {Ppe, PpeRelations} from '../models';

export class PpeRepository extends DefaultCrudRepository<
  Ppe,
  typeof Ppe.prototype.id,
  PpeRelations
> {
  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
  ) {
    super(Ppe, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


  }
}
