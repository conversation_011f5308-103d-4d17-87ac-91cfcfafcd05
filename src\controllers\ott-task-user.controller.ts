import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  OttTask,
  User,
} from '../models';
import {OttTaskRepository} from '../repositories';

export class OttTaskUserController {
  constructor(
    @repository(OttTaskRepository)
    public ottTaskRepository: OttTaskRepository,
  ) { }

  @get('/ott-tasks/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to OttTask',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof OttTask.prototype.id,
  ): Promise<User> {
    return this.ottTaskRepository.dependent(id);
  }
}
