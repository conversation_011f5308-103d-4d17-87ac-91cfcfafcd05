import {Entity, hasMany, model, property} from '@loopback/repository';
import {Service} from './service.model';
import {TenantService} from './tenant-service.model';

@model()
export class Tenant extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  enterpriseName: string;

  @property({
    type: 'string',
  })
  enterpriseId?: string;

  @property({
    type: 'number',
  })
  numberOfEmployees?: number;

  @property({
    type: 'string',
  })
  logo?: string;

  @property({
    type: 'string',
  })
  s3Bucket?: string;

  @property({
    type: 'string',
  })
  CDN?: string;

  @property({
    type: 'string',
  })
  userAwsPoolId?: string;

  @property({
    type: 'string',
  })
  userAwsClientId?: string;

  @property({
    type: 'string',
  })
  userAwsCognitoAccessKey?: string;

  @property({
    type: 'string',
  })
  userAwsCognitoSecretKey?: string;

  @property({
    type: 'string',
  })
  userAwsUserPoolDomain?: string;

  @property({
    type: 'string',
  })
  adminAwsPoolId?: string;

  @property({
    type: 'string',
  })
  adminAwsClientId?: string;

  @property({
    type: 'string',
  })
  adminAwsCognitoAccessKey?: string;

  @property({
    type: 'string',
  })
  adminAwsCognitoSecretKey?: string;

  @property({
    type: 'string',
  })
  adminAwsUserPoolDomain?: string;

  @property({
    type: 'string',
  })
  adminAzureClientId?: string;

  @property({
    type: 'string',
  })
  adminAzureClientSecret?: string;

  @property({
    type: 'string',
  })
  adminAzureTenantId?: string;

  @property({
    type: 'string',
  })
  externalUserPoolId?: string;

  @property({
    type: 'string',
  })
  awsRegion?: string;

  @property({
    type: 'string',
  })
  dbName?: string;

  @property({
    type: 'string',
  })
  password?: string;

  @property({
    type: 'string',
  })
  subDomain?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  @hasMany(() => Service, {through: {model: () => TenantService}})
  services: Service[];

  constructor(data?: Partial<Tenant>) {
    super(data);
  }
}

export interface TenantRelations {
  // describe navigational properties here
}

export type TenantWithRelations = Tenant & TenantRelations;
