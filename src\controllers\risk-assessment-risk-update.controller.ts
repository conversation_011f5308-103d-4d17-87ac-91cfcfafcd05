import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  RiskAssessment,
  RiskUpdate,
} from '../models';
import {RiskAssessmentRepository} from '../repositories';

export class RiskAssessmentRiskUpdateController {
  constructor(
    @repository(RiskAssessmentRepository) protected riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @get('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'Array of RiskAssessment has many RiskUpdate',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(RiskUpdate)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<RiskUpdate>,
  ): Promise<RiskUpdate[]> {
    return this.riskAssessmentRepository.riskUpdates(id).find(filter);
  }

  @post('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'RiskAssessment model instance',
        content: {'application/json': {schema: getModelSchemaRef(RiskUpdate)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, {
            title: 'NewRiskUpdateInRiskAssessment',
            exclude: ['id'],
            optional: ['riskAssessmentId']
          }),
        },
      },
    }) riskUpdate: Omit<RiskUpdate, 'id'>,
  ): Promise<RiskUpdate> {
    return this.riskAssessmentRepository.riskUpdates(id).create(riskUpdate);
  }

  @patch('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'RiskAssessment.RiskUpdate PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, {partial: true}),
        },
      },
    })
    riskUpdate: Partial<RiskUpdate>,
    @param.query.object('where', getWhereSchemaFor(RiskUpdate)) where?: Where<RiskUpdate>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.riskUpdates(id).patch(riskUpdate, where);
  }

  @del('/risk-assessments/{id}/risk-updates', {
    responses: {
      '200': {
        description: 'RiskAssessment.RiskUpdate DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(RiskUpdate)) where?: Where<RiskUpdate>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.riskUpdates(id).delete(where);
  }
}
