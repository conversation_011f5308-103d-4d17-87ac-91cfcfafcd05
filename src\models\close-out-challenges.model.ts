import {Entity, model, property} from '@loopback/repository';

@model()
export class CloseOutChallenges extends Entity {
  @property({
    type: 'string',
  })
  unexpectedChallenges?: string;

  @property({
    type: 'string',
  })
  remarks?: string;


  constructor(data?: Partial<CloseOutChallenges>) {
    super(data);
  }
}

export interface CloseOutChallengesRelations {
  // describe navigational properties here
}

export type CloseOutChallengesWithRelations = CloseOutChallenges & CloseOutChallengesRelations;
