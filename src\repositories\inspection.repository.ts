import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {Inspection, InspectionRelations, User, Action, Checklist, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix} from '../models';
import {UserRepository} from './user.repository';
import {ActionRepository} from './action.repository';
import {ChecklistRepository} from './checklist.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';

export class InspectionRepository extends DefaultCrudRepository<
  Inspection,
  typeof Inspection.prototype.id,
  InspectionRelations
> {

  public readonly inspector: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly assignedBy: BelongsToAccessor<User, typeof Inspection.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof Inspection.prototype.id>;

  public readonly checklist: BelongsToAccessor<Checklist, typeof Inspection.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof Inspection.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof Inspection.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof Inspection.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof Inspection.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof Inspection.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof Inspection.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>, @repository.getter('ChecklistRepository') protected checklistRepositoryGetter: Getter<ChecklistRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>,
  ) {
    super(Inspection, dataSource);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.checklist = this.createBelongsToAccessorFor('checklist', checklistRepositoryGetter,);
    this.registerInclusionResolver('checklist', this.checklist.inclusionResolver);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.assignedBy = this.createBelongsToAccessorFor('assignedBy', userRepositoryGetter,);
    this.registerInclusionResolver('assignedBy', this.assignedBy.inclusionResolver);
    this.inspector = this.createBelongsToAccessorFor('inspector', userRepositoryGetter,);
    this.registerInclusionResolver('inspector', this.inspector.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
