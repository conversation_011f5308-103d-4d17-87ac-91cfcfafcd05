import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {Action} from './action.model';
import {LocationFive} from './location-five.model';
import {LocationFour} from './location-four.model';
import {LocationOne} from './location-one.model';
import {LocationSix} from './location-six.model';
import {LocationThree} from './location-three.model';
import {LocationTwo} from './location-two.model';
import {User} from './user.model';

@model()
export class ObservationReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  observationCategory?: 'Environment' | 'Health' | 'Safety' | 'Others';

  @property({
    type: 'string',
  })
  observationType?: 'Safe' | 'Unsafe';

  @property({
    type: 'string',

  })
  maskId?: string;

  @property({
    type: 'string',
  })
  observationActOrCondition?: 'Act' | 'Condition';

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  status?: 'Initiated' | 'Rectified on Spot' | 'In Review' | 'Action in Progress' | 'Action Completed & Closed' | 'Returned' | 'Completed' | 'Closed';

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  qrSessionId?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  evidence?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'date',

  })
  dueDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;


  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'boolean',
  })
  rectifiedOnSpot?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  isQR?: boolean;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'boolean',
  })
  isReviewerRequired?: boolean;

  @belongsTo(() => User)
  reporterId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => User)
  actionOwnerId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @hasMany(() => Action, {keyTo: 'objectId'})
  observationActions: Action[];

  constructor(data?: Partial<ObservationReport>) {
    super(data);
  }
}

export interface ObservationReportRelations {
  // describe navigational properties here
}

export type ObservationReportWithRelations = ObservationReport & ObservationReportRelations;
