import {Entity, model, property} from '@loopback/repository';

@model()
export class HistoryData extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  serviceName?: string;

  @property({
    type: 'string',
    required: true,
  })
  maskId: string;

  @property({
    type: 'any',
  })
  data?: any;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<HistoryData>) {
    super(data);
  }
}

export interface HistoryDataRelations {
  // describe navigational properties here
}

export type HistoryDataWithRelations = HistoryData & HistoryDataRelations;
