import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {UserLocation} from '../models';
import {UserLocationRepository} from '../repositories';

export class UserLocationController {
  constructor(
    @repository(UserLocationRepository)
    public userLocationRepository : UserLocationRepository,
  ) {}

  @post('/user-locations')
  @response(200, {
    description: 'UserLocation model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserLocation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocation, {
            title: 'NewUserLocation',
            exclude: ['id'],
          }),
        },
      },
    })
    userLocation: Omit<UserLocation, 'id'>,
  ): Promise<UserLocation> {
    return this.userLocationRepository.create(userLocation);
  }

  @get('/user-locations/count')
  @response(200, {
    description: 'UserLocation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(UserLocation) where?: Where<UserLocation>,
  ): Promise<Count> {
    return this.userLocationRepository.count(where);
  }

  @get('/user-locations')
  @response(200, {
    description: 'Array of UserLocation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserLocation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserLocation) filter?: Filter<UserLocation>,
  ): Promise<UserLocation[]> {
    return this.userLocationRepository.find(filter);
  }

  @patch('/user-locations')
  @response(200, {
    description: 'UserLocation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocation, {partial: true}),
        },
      },
    })
    userLocation: UserLocation,
    @param.where(UserLocation) where?: Where<UserLocation>,
  ): Promise<Count> {
    return this.userLocationRepository.updateAll(userLocation, where);
  }

  @get('/user-locations/{id}')
  @response(200, {
    description: 'UserLocation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserLocation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserLocation, {exclude: 'where'}) filter?: FilterExcludingWhere<UserLocation>
  ): Promise<UserLocation> {
    return this.userLocationRepository.findById(id, filter);
  }

  @patch('/user-locations/{id}')
  @response(204, {
    description: 'UserLocation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserLocation, {partial: true}),
        },
      },
    })
    userLocation: UserLocation,
  ): Promise<void> {
    await this.userLocationRepository.updateById(id, userLocation);
  }

  @put('/user-locations/{id}')
  @response(204, {
    description: 'UserLocation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userLocation: UserLocation,
  ): Promise<void> {
    await this.userLocationRepository.replaceById(id, userLocation);
  }

  @del('/user-locations/{id}')
  @response(204, {
    description: 'UserLocation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userLocationRepository.deleteById(id);
  }
}
