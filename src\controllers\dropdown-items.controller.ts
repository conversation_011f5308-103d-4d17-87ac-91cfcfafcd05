import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DropdownItems} from '../models';
import {DropdownItemsRepository} from '../repositories';

export class DropdownItemsController {
  constructor(
    @repository(DropdownItemsRepository)
    public dropdownItemsRepository : DropdownItemsRepository,
  ) {}

  @post('/dropdown-items')
  @response(200, {
    description: 'DropdownItems model instance',
    content: {'application/json': {schema: getModelSchemaRef(DropdownItems)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropdownItems, {
            title: 'NewDropdownItems',
            exclude: ['id'],
          }),
        },
      },
    })
    dropdownItems: Omit<DropdownItems, 'id'>,
  ): Promise<DropdownItems> {
    return this.dropdownItemsRepository.create(dropdownItems);
  }

  @get('/dropdown-items/count')
  @response(200, {
    description: 'DropdownItems model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DropdownItems) where?: Where<DropdownItems>,
  ): Promise<Count> {
    return this.dropdownItemsRepository.count(where);
  }

  @get('/dropdown-items')
  @response(200, {
    description: 'Array of DropdownItems model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DropdownItems, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DropdownItems) filter?: Filter<DropdownItems>,
  ): Promise<DropdownItems[]> {
    return this.dropdownItemsRepository.find(filter);
  }

  @patch('/dropdown-items')
  @response(200, {
    description: 'DropdownItems PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropdownItems, {partial: true}),
        },
      },
    })
    dropdownItems: DropdownItems,
    @param.where(DropdownItems) where?: Where<DropdownItems>,
  ): Promise<Count> {
    return this.dropdownItemsRepository.updateAll(dropdownItems, where);
  }

  @get('/dropdown-items/{id}')
  @response(200, {
    description: 'DropdownItems model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DropdownItems, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DropdownItems, {exclude: 'where'}) filter?: FilterExcludingWhere<DropdownItems>
  ): Promise<DropdownItems> {
    return this.dropdownItemsRepository.findById(id, filter);
  }

  @patch('/dropdown-items/{id}')
  @response(204, {
    description: 'DropdownItems PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropdownItems, {partial: true}),
        },
      },
    })
    dropdownItems: DropdownItems,
  ): Promise<void> {
    await this.dropdownItemsRepository.updateById(id, dropdownItems);
  }

  @put('/dropdown-items/{id}')
  @response(204, {
    description: 'DropdownItems PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() dropdownItems: DropdownItems,
  ): Promise<void> {
    await this.dropdownItemsRepository.replaceById(id, dropdownItems);
  }

  @del('/dropdown-items/{id}')
  @response(204, {
    description: 'DropdownItems DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.dropdownItemsRepository.deleteById(id);
  }
}
