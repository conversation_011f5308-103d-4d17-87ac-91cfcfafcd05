import {Entity, hasMany, model, property} from '@loopback/repository';
import {LocationSix} from './location-six.model';

@model()
export class LocationFive extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  locationFourId?: string;

  @hasMany(() => LocationSix)
  locationSixes: LocationSix[];

  constructor(data?: Partial<LocationFive>) {
    super(data);
  }
}

export interface LocationFiveRelations {
  // describe navigational properties here
}

export type LocationFiveWithRelations = LocationFive & LocationFiveRelations;
