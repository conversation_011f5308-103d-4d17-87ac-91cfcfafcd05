import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  LocationFive,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionLocationFiveController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to Inspection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFive),
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<LocationFive> {
    return this.inspectionRepository.locationFive(id);
  }
}
