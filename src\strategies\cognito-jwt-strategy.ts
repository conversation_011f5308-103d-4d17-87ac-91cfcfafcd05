import {AuthenticationBindings, AuthenticationStrategy} from '@loopback/authentication';
import {bind} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, Request} from '@loopback/rest';
import {securityId, UserProfile} from '@loopback/security';
import {CognitoJwtVerifier} from "aws-jwt-verify";
import axios from 'axios';
import {decode} from 'jsonwebtoken';
import {ConfigRepository, UserRepository} from '../repositories';

@bind({tags: {namespace: AuthenticationBindings.STRATEGY}})
export class CognitoJwtAuthenticationStrategy implements AuthenticationStrategy {
  name = 'cognito-jwt';

  private region!: string;
  private userPoolId!: string;
  private clientId!: string;
  private domain!: string;

  constructor(
    @repository(ConfigRepository)
    public configRepository: ConfigRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,

  ) {

  }

  async loadConfig() {


    this.region = await this.getConfigValue('COGNITO_REGION');
    this.userPoolId = await this.getConfigValue('COGNITO_USER_POOL_ID');
    this.clientId = await this.getConfigValue('COGNITO_USER_APP_CLIENT_ID');
    this.domain = await this.getConfigValue('COGNITO_USER_DOMAIN');
  }

  private async getConfigValue(key: string): Promise<string> {
    const value = await this.configRepository.getConfigValue(key);
    if (value === undefined) {
      throw new Error(`Configuration value for key ${key} not found`);
    }
    return value;
  }

  async authenticate(request: Request): Promise<UserProfile | undefined> {

    await this.loadConfig();

    console.log(this.region, this.userPoolId, this.clientId, this.domain)

    const token = this.extractCredentials(request);
    if (!token) {
      throw new HttpErrors.Unauthorized('Token not found');
    }


    const decoded = decode(token, {complete: true});
    if (!decoded || typeof decoded === 'string' || !decoded.header) {
      throw new HttpErrors.Unauthorized('Token decoding failed');
    }

    const verifier = CognitoJwtVerifier.create({
      userPoolId: `${this.userPoolId}`,
      tokenUse: "access",
      clientId: `${this.clientId}`,
    });

    try {
      const payload = await verifier.verify(
        token // the JWT as string
      );

      const userProfile: UserProfile = await this.convertToUserProfile(payload, token);

      return userProfile;
    } catch (e) {
      console.log("Token not valid!", e);
    }

  }

  extractCredentials(request: Request): string {
    if (!request.headers.authorization) {
      throw new HttpErrors.Unauthorized('Authorization header is missing');
    }

    const authHeaderValue = request.headers.authorization;
    if (!authHeaderValue.startsWith('Bearer ')) {
      throw new HttpErrors.Unauthorized('Authorization header is not of type Bearer');
    }

    return authHeaderValue.replace('Bearer ', '').trim();
  }

  async convertToUserProfile(payload: any, token: string): Promise<UserProfile> {

    const response = await axios.get(`${this.domain}/oauth2/userInfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const userInfo = response.data;
    const userData = await this.userRepository.findOne({where: {email: userInfo.email}});

    if (!userData) {
      throw new HttpErrors.Unauthorized('User not found');
    }

    if (!userData.id) {
      throw new HttpErrors.Unauthorized('User not found');
    }


    const userProfile: UserProfile = {
      [securityId]: userData.id,
      id: userData.id,
      name: payload.username,
      email: userInfo.email,
    };
    return userProfile;
  }
}
