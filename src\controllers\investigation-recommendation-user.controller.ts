import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  InvestigationRecommendation,
  User,
} from '../models';
import {InvestigationRecommendationRepository} from '../repositories';

export class InvestigationRecommendationUserController {
  constructor(
    @repository(InvestigationRecommendationRepository)
    public investigationRecommendationRepository: InvestigationRecommendationRepository,
  ) { }

  @get('/investigation-recommendations/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to InvestigationRecommendation',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof InvestigationRecommendation.prototype.id,
  ): Promise<User> {
    return this.investigationRecommendationRepository.assignedTo(id);
  }
}
