import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {LocationThree, LocationTwo, LocationTwoRelations} from '../models';
import {LocationThreeRepository} from './location-three.repository';

export class LocationTwoRepository extends DefaultCrudRepository<
  LocationTwo,
  typeof LocationTwo.prototype.id,
  LocationTwoRelations
> {

  public readonly locationThrees: HasManyRepositoryFactory<LocationThree, typeof LocationTwo.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>,
  ) {
    super(LocationTwo, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.locationThrees = this.createHasManyRepositoryFactoryFor('locationThrees', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThrees', this.locationThrees.inclusionResolver);
  }
}
