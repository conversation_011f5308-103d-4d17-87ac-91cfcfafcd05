import {belongsTo, Entity, model, property} from '@loopback/repository';
import {User} from './user.model';

@model()
export class ToolboxSignStatus extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'date',
  })
  signedDate?: string;

  @property({
    type: 'string',
  })
  sign?: string;


  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => User)
  signedById: string;


  @property({
    type: 'string',
  })
  toolboxTalkId?: string;

  constructor(data?: Partial<ToolboxSignStatus>) {
    super(data);
  }
}

export interface ToolboxSignStatusRelations {
  // describe navigational properties here
}

export type ToolboxSignStatusWithRelations = ToolboxSignStatus & ToolboxSignStatusRelations;
