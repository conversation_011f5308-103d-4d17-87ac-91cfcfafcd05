import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Service,
  Tenant
} from '../models';
import {TenantRepository} from '../repositories';

// import {authenticate} from '@loopback/authentication';

// @authenticate('cognito-jwt')
export class TenantServiceController {
  constructor(
    @repository(TenantRepository) protected tenantRepository: TenantRepository,
  ) { }

  @get('/tenants/{id}/services', {
    responses: {
      '200': {
        description: 'Array of Tenant has many Service through TenantService',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Service)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Service>,
  ): Promise<Service[]> {
    return this.tenantRepository.services(id).find(filter);
  }

  @post('/tenants/{id}/services', {
    responses: {
      '200': {
        description: 'create a Service model instance',
        content: {'application/json': {schema: getModelSchemaRef(Service)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Tenant.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Service, {
            title: 'NewServiceInTenant',
            exclude: ['id'],
          }),
        },
      },
    }) service: Omit<Service, 'id'>,
  ): Promise<Service> {
    return this.tenantRepository.services(id).create(service);
  }

  @patch('/tenants/{id}/services', {
    responses: {
      '200': {
        description: 'Tenant.Service PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Service, {partial: true}),
        },
      },
    })
    service: Partial<Service>,
    @param.query.object('where', getWhereSchemaFor(Service)) where?: Where<Service>,
  ): Promise<Count> {
    return this.tenantRepository.services(id).patch(service, where);
  }

  @del('/tenants/{id}/services', {
    responses: {
      '200': {
        description: 'Tenant.Service DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Service)) where?: Where<Service>,
  ): Promise<Count> {
    return this.tenantRepository.services(id).delete(where);
  }
}
