import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, Incident, Investigation, InvestigationControl} from '../models';
import {ActionRepository, HistoryDataRepository, IncidentRepository, InvestigationRepository, ServiceRepository} from '../repositories';
import {LocationFilterService} from '../services/location-filter.service';
const SERVICE_NAME = 'INCINV';

@authenticate('cognito-jwt')
export class IncidentController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
    @repository(InvestigationRepository)
    public investigationRepository: InvestigationRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @repository(HistoryDataRepository)
    public historyDataRepository: HistoryDataRepository,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,
  ) { }

  @post('/incidents')
  @response(200, {
    description: 'Incident model instance',
    content: {'application/json': {schema: getModelSchemaRef(Incident)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Incident, {
            title: 'NewIncident',
            exclude: ['id'],
          }),
        },
      },
    })
    incident: Omit<Incident, 'id'>,
  ): Promise<Incident> {
    incident.reportedById = currentUserProfile[securityId];
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.incidentRepository.count();
    incident.maskId = `${SERVICE_NAME}-${year}${month}${day}-${count.count + 1}`;
    incident.status = 'Under Review';
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const incidentData = await this.incidentRepository.create(incident);
    const actions: Partial<Action> =
    {
      application: SERVICE_NAME,
      actionType: 'review_incident',
      actionToBeTaken: 'Review Incident',
      description: incidentData.description,
      maskId: incidentData.maskId,
      trackId: uuidv4(), // Generate unique id
      sequence: '1',
      prefix: 'INCINV',
      applicationId: incidentData.id,
      dueDate: '',
      objectId: incidentData.id,
      submittedById: currentUserProfile[securityId],
      assignedToId: [incidentData.reviewerId],
      submitURL: '/submit-incident-report',
      status: 'Initiated',
      serviceId: service.id,

    };


    // Insert into actionRepository
    await this.actionRepository.create(actions);
    return incidentData;

  }

  @get('/incidents/count')
  @response(200, {
    description: 'Incident model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Incident) where?: Where<Incident>,
  ): Promise<Count> {
    return this.incidentRepository.count(where);
  }

  @get('/incidents')
  @response(200, {
    description: 'Array of Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Incident, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Incident) filter?: Filter<Incident>,
  ): Promise<Incident[]> {


    const incidentData = await this.incidentRepository.find({...filter});
    const modifiedIncident = await Promise.all(
      incidentData.map(async (data) => {

        // Then fetch actions for each audit finding and include the auditFindings data in each action

        const totalActions = await this.actionRepository.find({where: {applicationId: data.id}});
        // Map each action to include the auditFinding data under applicationDetails


        // Filter actions to find those that are completed
        const completedActions = totalActions.filter(action => action.status === 'Completed');

        // Create an instance of ReportIncident with the desired properties
        const modifiedReport = new Incident({
          ...data,

          totalActions: totalActions,
          completedActions: completedActions

        });

        return modifiedReport;
      })
    );

    return modifiedIncident;

  }

  @patch('/incidents')
  @response(200, {
    description: 'Incident PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Incident, {partial: true}),
        },
      },
    })
    incident: Incident,
    @param.where(Incident) where?: Where<Incident>,
  ): Promise<Count> {
    return this.incidentRepository.updateAll(incident, where);
  }

  @get('/incidents/{id}')
  @response(200, {
    description: 'Incident model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Incident, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Incident, {exclude: 'where'}) filter?: FilterExcludingWhere<Incident>
  ): Promise<Incident> {
    return this.incidentRepository.findById(id, filter);
  }

  @patch('/incidents/{id}')
  @response(204, {
    description: 'Incident PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Incident, {partial: true}),
        },
      },
    })
    incident: Incident,
  ): Promise<void> {
    await this.incidentRepository.updateById(id, incident);
  }


  @patch('/incidents-investigation/{id}')
  @response(204, {
    description: 'Incident PATCH success',
  })
  async updateIncidentByInvestigationById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Incident, {partial: true}),
        },
      },
    })
    incident: Incident,
  ): Promise<void> {

    const identifiedPreventiveControls = incident.preventiveControls?.identifiedPreventiveControls ?? [];
    const unidentifiedPreventiveControls = incident.preventiveControls?.unIdentifiedPreventiveControls ?? [];
    const identifiedMitigativeControls = incident.mitigativeControls?.identifiedMitigativeControls ?? [];
    const unidentifiedMitigativeControls = incident.mitigativeControls?.unIdentifiedMitigativeControls ?? [];

    const investigationControls = new InvestigationControl({
      identifiedPreventiveControls: identifiedPreventiveControls,
      unidentifiedPreventiveControls: unidentifiedPreventiveControls,
      identifiedMitigativeControls: identifiedMitigativeControls,
      unidentifiedMitigativeControls: unidentifiedMitigativeControls,
    });

    // Corrected this line
    await this.investigationRepository.updateAll(
      {controls: investigationControls},
      {incidentId: id}
    );

    await this.incidentRepository.updateById(id, incident);
  }





  @patch('/trigger-investigation/{id}')
  @response(204, {
    description: 'Incident PATCH success',
  })
  async triggerInvestigationById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Incident, {partial: true}),
        },
      },
    })
    incident: Incident,
  ): Promise<Investigation> {
    await this.incidentRepository.updateById(id, {isInvestigationRequired: incident.isInvestigationRequired, investigatorId: incident.investigatorId, status: 'Under Investigation'});

    const incidentHistory = await this.incidentRepository.findById(id, {
      include: [
        {relation: 'investigationRecords'},
        {relation: 'locationOne'},
        {relation: 'locationTwo'},
        {relation: 'locationThree'},
        {relation: 'locationFour'},
        {relation: 'locationFive'},
        {relation: 'locationSix'},
        {relation: 'workActivity'},
        {relation: 'reportedBy'},
        {relation: 'investigator'},
        {relation: 'reviewer'},
        {relation: 'personnel'},
        {relation: 'environment'},
        {relation: 'property'},
        {relation: 'operation'},
        {relation: 'nearTermControlMeasures'},
        {relation: 'investigation'}
      ]
    });

    await this.historyDataRepository.create({serviceName: SERVICE_NAME, maskId: incidentHistory.maskId, data: incidentHistory})

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const incidentData = await this.incidentRepository.findById(id);
    if (!incidentData) {
      throw new Error('Incident Data not found!')
    }
    const identifiedPreventiveControls = incidentData.preventiveControls?.identifiedPreventiveControls ?? [];
    const unidentifiedPreventiveControls = incidentData.preventiveControls?.unIdentifiedPreventiveControls ?? [];
    const identifiedMitigativeControls = incidentData.mitigativeControls?.identifiedMitigativeControls ?? [];
    const unidentifiedMitigativeControls = incidentData.mitigativeControls?.unIdentifiedMitigativeControls ?? [];
    const investigationControls = new InvestigationControl({
      identifiedPreventiveControls: identifiedPreventiveControls,
      unidentifiedPreventiveControls: unidentifiedPreventiveControls,
      identifiedMitigativeControls: identifiedMitigativeControls,
      unidentifiedMitigativeControls: unidentifiedMitigativeControls,
    })
    const createInvestigationData = new Investigation({
      status: 'Not Started',
      controls: investigationControls,
      incidentId: incidentData?.id ?? '',
      investigatorId: incident.investigatorId
    });

    const investigationData = await this.investigationRepository.create(createInvestigationData)

    const actions: Partial<Action> =
    {
      application: SERVICE_NAME,
      actionType: 'conduct_investigation',
      actionToBeTaken: 'Conduct Investigation',
      description: incidentData.description,
      maskId: incidentData.maskId,
      trackId: uuidv4(), // Generate unique id
      sequence: '1',
      prefix: 'INCINV',
      applicationId: incidentData.id,
      dueDate: '',
      objectId: investigationData.id,
      submittedById: currentUserProfile[securityId],
      assignedToId: [incidentData.investigatorId],
      submitURL: '/submit-investigation-for-approval',
      status: 'Initiated',
      serviceId: service.id,

    };


    // Insert into actionRepository
    await this.actionRepository.create(actions);

    return investigationData;

  }

  @put('/incidents/{id}')
  @response(204, {
    description: 'Incident PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() incident: Incident,
  ): Promise<void> {
    await this.incidentRepository.replaceById(id, incident);
  }

  @del('/incidents/{id}')
  @response(204, {
    description: 'Incident DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.incidentRepository.deleteById(id);
  }
}
