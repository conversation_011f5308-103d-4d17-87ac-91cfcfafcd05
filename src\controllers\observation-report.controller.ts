import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {v4 as uuidv4} from 'uuid';
import {Action, ObservationReport} from '../models';
import {ActionRepository, ObservationReportRepository, ServiceRepository, UserRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';

const SERVICE_NAME = 'OBS';

@authenticate('cognito-jwt')
export class ObservationReportController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(ServiceRepository)
    public serviceRepository: ServiceRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.SqsService')
    private sqsService: SqsService,
  ) { }

  @post('/observation-reports')
  @response(200, {
    description: 'ObservationReport model instance',
    content: {'application/json': {schema: getModelSchemaRef(ObservationReport)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {
            title: 'NewObservationReport',
            exclude: ['id'],
          }),
        },
      },
    })
    observationReport: Omit<ObservationReport, 'id'>,
  ): Promise<ObservationReport> {

    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.observationReportRepository.count();

    observationReport.reporterId = currentUserProfile[securityId]
    observationReport.maskId = `${SERVICE_NAME}-${year}${month}${day}-${count.count + 1}`;
    const obsData = await this.observationReportRepository.create(observationReport);

    if (observationReport.rectifiedOnSpot) {
      observationReport.status = 'Rectified on Spot';

    } else {
      if (observationReport.observationType === 'Safe') {
        observationReport.status = 'Completed';
      }
      else if (observationReport.isReviewerRequired) {
        observationReport.status = 'In Review';
        const action: Partial<Action> = {

          application: SERVICE_NAME,
          actionType: 'review',
          actionToBeTaken: 'Review Observation',
          description: observationReport.description,
          maskId: observationReport.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'OBS',
          applicationId: obsData.id,
          objectId: obsData.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [observationReport.reviewerId],
          submitURL: '/observation-action-submit',
          status: 'Initiated',
          serviceId: service.id,


        }


        await this.actionRepository.create(action);

        // Send notification to reviewer
        if (observationReport.reviewerId) {
          const reviewer = await this.userRepository.findById(observationReport.reviewerId);
          if (reviewer && reviewer.email) {
            const mailSubject = `Review Required - ${observationReport.maskId}`;
            const mailBody = `
              <h4>Dear ${reviewer.firstName ?? 'User'},</h4>
              <p>You have been assigned to review an Observation Report ${observationReport.maskId}.</p>
              <p><strong>Description:</strong> ${observationReport.description}</p>
              <p>Please review the observation and take appropriate action.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(reviewer, mailSubject, mailBody);
          }
        }

      } else {
        observationReport.status = 'Action in Progress';

        const action: Partial<Action> = {

          application: SERVICE_NAME,
          actionType: 'take_action',
          actionToBeTaken: observationReport.actionToBeTaken,
          description: observationReport.description,
          maskId: observationReport.maskId,
          trackId: uuidv4(), // Generate unique id
          sequence: '1',
          prefix: 'OBS',
          applicationId: obsData.id,
          dueDate: observationReport.dueDate,
          objectId: obsData.id,
          submittedById: currentUserProfile[securityId],
          assignedToId: [observationReport.actionOwnerId],
          submitURL: '/observation-action-submit',
          status: 'Initiated',
          serviceId: service.id,


        }
        await this.actionRepository.create(action);

        // Send notification to action owner
        if (observationReport.actionOwnerId) {
          const actionOwner = await this.userRepository.findById(observationReport.actionOwnerId);
          if (actionOwner && actionOwner.email) {
            const mailSubject = `New Action Required - ${observationReport.maskId}`;
            const mailBody = `
              <h4>Dear ${actionOwner.firstName ?? 'User'},</h4>
              <p>A new action has been assigned to you for Observation Report ${observationReport.maskId}.</p>
              <p><strong>Action to be taken:</strong> ${observationReport.actionToBeTaken}</p>
              <p><strong>Description:</strong> ${observationReport.description}</p>
              <p><strong>Due Date:</strong> ${observationReport.dueDate ? new Date(observationReport.dueDate).toLocaleDateString() : 'Not specified'}</p>
              <p>Please take the necessary action and update the status once completed.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody);
          }
        }
      }
    }

    await this.observationReportRepository.updateById(obsData.id, {status: observationReport.status});
    return obsData;
  }

  @patch('/observation-action-submit/{actionId}')
  @response(204, {
    description: 'Observation PATCH success',
  })
  async submitByActionId(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('actionId') actionId: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    const service = await this.serviceRepository.findOne({where: {maskName: SERVICE_NAME}})

    if (!service) {
      throw new Error('Service not found')
    }

    const actionData = await this.actionRepository.findById(actionId)
    if (!actionData) {
      throw new Error('No Action Data Found')
    }

    const obsData = await this.observationReportRepository.findById(actionData.objectId)
    if (!obsData) {
      throw new Error('No OBS Data Found')
    }


    switch (actionData.actionType) {
      case 'review': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'take_action',
          actionToBeTaken: observationReport.actionToBeTaken,
          description: obsData.description,
          maskId: obsData.maskId,
          trackId: actionData.trackId,
          sequence: '1',
          prefix: 'OBS',
          applicationId: obsData.id,
          objectId: obsData.id,
          dueDate: observationReport.dueDate,
          submittedById: currentUserProfile[securityId],
          assignedToId: [observationReport.actionOwnerId],
          submitURL: '/observation-action-submit',
          status: 'Initiated',
          serviceId: service.id
        };
        observationReport.status = "Action in Progress"

        // Insert into actionRepository
        await this.observationReportRepository.updateById(actionData.applicationId, {actionOwnerId: observationReport.actionOwnerId, dueDate: observationReport.dueDate, actionToBeTaken: observationReport.actionToBeTaken});
        await this.actionRepository.create(actions);

        // Send notification to action owner
        if (observationReport.actionOwnerId) {
          const actionOwner = await this.userRepository.findById(observationReport.actionOwnerId);
          if (actionOwner && actionOwner.email) {
            const mailSubject = `Action Required - ${obsData.maskId}`;
            const mailBody = `
              <h4>Dear ${actionOwner.firstName ?? 'User'},</h4>
              <p>An action has been assigned to you for Observation Report ${obsData.maskId} after review.</p>
              <p><strong>Action to be taken:</strong> ${observationReport.actionToBeTaken}</p>
              <p><strong>Description:</strong> ${obsData.description}</p>
              <p><strong>Due Date:</strong> ${observationReport.dueDate ? new Date(observationReport.dueDate).toLocaleDateString() : 'Not specified'}</p>
              <p>Please take the necessary action and update the status once completed.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody);
          }
        }
        break;
      }

      case 'take_action': {
        const actions: Partial<Action> = {
          application: SERVICE_NAME,
          actionType: 'verify_action',
          actionToBeTaken: obsData.actionToBeTaken,
          actionTaken: observationReport.actionTaken,
          uploads: observationReport.evidence,
          description: obsData.description,
          maskId: obsData.maskId,
          trackId: actionData.trackId,
          sequence: '1',
          prefix: 'OBS',
          applicationId: obsData.id,
          objectId: obsData.id,
          dueDate: observationReport.dueDate,
          submittedById: currentUserProfile[securityId],
          assignedToId: [observationReport.reviewerId],
          submitURL: '/observation-action-submit',
          status: 'Initiated',
          serviceId: service.id
        };
        observationReport.status = "In Review"

        await this.actionRepository.updateById(actionData.id, {actionTaken: observationReport.actionTaken, uploads: observationReport.evidence})
        // Insert into actionRepository
        await this.observationReportRepository.updateById(actionData.applicationId, {actionTaken: observationReport.actionTaken, evidence: observationReport.evidence, dueDate: observationReport.dueDate, reviewerId: observationReport.reviewerId});
        await this.actionRepository.create(actions);

        // Send notification to reviewer
        if (observationReport.reviewerId) {
          const reviewer = await this.userRepository.findById(observationReport.reviewerId);
          if (reviewer && reviewer.email) {
            const mailSubject = `Action Verification Required - ${obsData.maskId}`;
            const mailBody = `
              <h4>Dear ${reviewer.firstName ?? 'User'},</h4>
              <p>An action has been completed for Observation Report ${obsData.maskId} and requires your verification.</p>
              <p><strong>Action Taken:</strong> ${observationReport.actionTaken}</p>
              <p><strong>Description:</strong> ${obsData.description}</p>
              <p>Please verify the action taken and update the status accordingly.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(reviewer, mailSubject, mailBody);
          }
        }

        // Also notify the reporter
        if (obsData.reporterId) {
          const reporter = await this.userRepository.findById(obsData.reporterId);
          if (reporter && reporter.email) {
            const mailSubject = `Action Update - ${obsData.maskId}`;
            const mailBody = `
              <h4>Dear ${reporter.firstName ?? 'User'},</h4>
              <p>An action for Observation Report ${obsData.maskId} that you reported has been completed and is now under review.</p>
              <p><strong>Action Taken:</strong> ${observationReport.actionTaken}</p>
              <p>You will be notified once the action is verified.</p>
              <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
            `;
            await this.sqsService.sendMessage(reporter, mailSubject, mailBody);
          }
        }
        break;
      }

      case 'reperform_action':
        {
          const actions: Partial<Action> = {
            application: SERVICE_NAME,
            actionType: 'verify_action',
            actionToBeTaken: 'Review Action Completion',
            actionTaken: observationReport.actionTaken,
            uploads: observationReport.evidence,
            description: obsData.description,
            maskId: obsData.maskId,
            trackId: actionData.trackId,
            sequence: actionData.sequence,
            prefix: 'OBS',
            applicationId: obsData.id,
            objectId: obsData.id,
            submittedById: currentUserProfile[securityId],
            assignedToId: [observationReport.reviewerId],
            submitURL: '/observation-action-submit',
            dueDate: obsData.dueDate,
            status: 'Initiated',
            serviceId: service.id
          };
          observationReport.status = "In Review"

          await this.actionRepository.updateById(actionData.id, {actionTaken: observationReport.actionTaken, uploads: observationReport.evidence})
          // Insert into actionRepository
          await this.observationReportRepository.updateById(actionData.applicationId, {actionTaken: observationReport.actionTaken, evidence: observationReport.evidence, reviewerId: observationReport.reviewerId});
          await this.actionRepository.create(actions);

          // Send notification to reviewer
          if (observationReport.reviewerId) {
            const reviewer = await this.userRepository.findById(observationReport.reviewerId);
            if (reviewer && reviewer.email) {
              const mailSubject = `Action Re-verification Required - ${obsData.maskId}`;
              const mailBody = `
                <h4>Dear ${reviewer.firstName ?? 'User'},</h4>
                <p>An action has been re-performed for Observation Report ${obsData.maskId} and requires your verification.</p>
                <p><strong>Action Taken:</strong> ${observationReport.actionTaken}</p>
                <p><strong>Description:</strong> ${obsData.description}</p>
                <p>Please verify the action taken and update the status accordingly.</p>
                <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
              `;
              await this.sqsService.sendMessage(reviewer, mailSubject, mailBody);
            }
          }
          break;
        }
      case 'verify_action':
        {
          if (observationReport.status !== 'Returned' && observationReport.status !== 'Completed') {
            throw new Error('Method Type Status not allowed')
          }

          switch (observationReport.status) {
            case 'Returned':
              {
                const actions: Partial<Action> =
                {
                  application: SERVICE_NAME,
                  actionType: 'reperform_action',
                  actionToBeTaken: 'Redo the Action',
                  description: obsData.description,
                  maskId: obsData.maskId,
                  comments: observationReport.comments,
                  trackId: uuidv4(), // Generate unique id
                  sequence: `${parseInt(actionData.sequence ?? '0') + 1}`,
                  prefix: 'OBS',
                  applicationId: obsData.id,
                  dueDate: obsData.dueDate,
                  objectId: obsData.id,
                  submittedById: currentUserProfile[securityId],
                  assignedToId: [obsData.actionOwnerId],
                  submitURL: '/observation-action-submit',
                  status: 'Initiated',
                  serviceId: service.id
                };
                await this.actionRepository.updateById(actionData.id, {comments: observationReport.comments, status: 'Completed'})

                observationReport.status = "Returned"
                // Insert into actionRepository
                await this.actionRepository.create(actions);

                // Send notification to action owner about returned action
                if (obsData.actionOwnerId) {
                  const actionOwner = await this.userRepository.findById(obsData.actionOwnerId);
                  if (actionOwner && actionOwner.email) {
                    const mailSubject = `Action Returned - ${obsData.maskId}`;
                    const mailBody = `
                      <h4>Dear ${actionOwner.firstName ?? 'User'},</h4>
                      <p>Your action for Observation Report ${obsData.maskId} has been reviewed and returned for further action.</p>
                      <p><strong>Comments:</strong> ${observationReport.comments}</p>
                      <p>Please address the comments and resubmit the action.</p>
                      <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
                    `;
                    await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody);
                  }
                }
                break;
              }
            case 'Completed':
              {
                observationReport.status = "Action Completed & Closed"

                // Send notification to reporter about completed action
                if (obsData.reporterId) {
                  const reporter = await this.userRepository.findById(obsData.reporterId);
                  if (reporter && reporter.email) {
                    const mailSubject = `Action Completed - ${obsData.maskId}`;
                    const mailBody = `
                      <h4>Dear ${reporter.firstName ?? 'User'},</h4>
                      <p>The action for Observation Report ${obsData.maskId} that you reported has been completed and verified.</p>
                      <p><strong>Action Taken:</strong> ${obsData.actionTaken}</p>
                      <p>The observation report is now closed.</p>
                      <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
                    `;
                    await this.sqsService.sendMessage(reporter, mailSubject, mailBody);
                  }
                }

                // Also notify the action owner
                if (obsData.actionOwnerId) {
                  const actionOwner = await this.userRepository.findById(obsData.actionOwnerId);
                  if (actionOwner && actionOwner.email) {
                    const mailSubject = `Action Verified and Completed - ${obsData.maskId}`;
                    const mailBody = `
                      <h4>Dear ${actionOwner.firstName ?? 'User'},</h4>
                      <p>Your action for Observation Report ${obsData.maskId} has been verified and completed.</p>
                      <p>The observation report is now closed.</p>
                      <p><i>This email is an automated notification. Please do not reply to this message.</i></p>
                    `;
                    await this.sqsService.sendMessage(actionOwner, mailSubject, mailBody);
                  }
                }
                break;
              }

            default: throw new Error('Method Type Status not allowed')
          }

          break;
        }

      default: throw new Error('Action type not allowed')
    }
    await this.actionRepository.updateById(actionId, {status: 'Completed'})
    //optimize the reviewer comments concept. it should be avaible only when reviewer submits it
    await this.observationReportRepository.updateById(actionData.applicationId, {status: observationReport.status});
  }

  @get('/observation-reports/count')
  @response(200, {
    description: 'ObservationReport model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.count(where);
  }

  @get('/observation-reports')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    return this.observationReportRepository.find(filter);
  }

  @get('/my-observation-reports')
  @response(200, {
    description: 'Array of ObservationReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ObservationReport, {includeRelations: true}),
        },
      },
    },
  })
  async findMyReports(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ObservationReport) filter?: Filter<ObservationReport>,
  ): Promise<ObservationReport[]> {
    const userId = currentUserProfile[securityId];
    return this.observationReportRepository.find({...filter, where: {...filter?.where, reporterId: userId}});
  }

  @patch('/observation-reports')
  @response(200, {
    description: 'ObservationReport PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: ObservationReport,
    @param.where(ObservationReport) where?: Where<ObservationReport>,
  ): Promise<Count> {
    return this.observationReportRepository.updateAll(observationReport, where);
  }

  @get('/observation-reports/{id}')
  @response(200, {
    description: 'ObservationReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ObservationReport, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ObservationReport, {exclude: 'where'}) filter?: FilterExcludingWhere<ObservationReport>
  ): Promise<ObservationReport> {
    return this.observationReportRepository.findById(id, filter);
  }

  @patch('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ObservationReport, {partial: true}),
        },
      },
    })
    observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.updateById(id, observationReport);
  }

  @put('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() observationReport: ObservationReport,
  ): Promise<void> {
    await this.observationReportRepository.replaceById(id, observationReport);
  }

  @del('/observation-reports/{id}')
  @response(204, {
    description: 'ObservationReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.observationReportRepository.deleteById(id);
  }
}
