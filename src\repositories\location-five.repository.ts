import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {LocationFive, LocationFiveRelations, LocationSix} from '../models';
import {LocationSixRepository} from './location-six.repository';

export class LocationFiveRepository extends DefaultCrudRepository<
  LocationFive,
  typeof LocationFive.prototype.id,
  LocationFiveRelations
> {

  public readonly locationSixes: HasManyRepositoryFactory<LocationSix, typeof LocationFive.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>,
  ) {
    super(LocationFive, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.locationSixes = this.createHasManyRepositoryFactoryFor('locationSixes', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSixes', this.locationSixes.inclusionResolver);
  }
}
