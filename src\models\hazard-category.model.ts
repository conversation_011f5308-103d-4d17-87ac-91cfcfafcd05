import {Entity, hasMany, model, property} from '@loopback/repository';
import {Hazards} from './hazards.model';

@model()
export class HazardCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
    default: 0,
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  @hasMany(() => Hazards)
  hazards: Hazards[];

  constructor(data?: Partial<HazardCategory>) {
    super(data);
  }
}

export interface HazardCategoryRelations {
  // describe navigational properties here
}

export type HazardCategoryWithRelations = HazardCategory & HazardCategoryRelations;
