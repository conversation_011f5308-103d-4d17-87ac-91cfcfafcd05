import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  LocationOne,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentLocationOneController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<LocationOne> {
    return this.incidentRepository.locationOne(id);
  }
}
