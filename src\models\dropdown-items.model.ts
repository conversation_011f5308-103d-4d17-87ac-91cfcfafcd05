import {belongsTo, Entity, model, property} from '@loopback/repository';
import {Dropdown} from './dropdown.model';

@model()
export class DropdownItems extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  maskName?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  placeholderName?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'number',
  })
  level?: number;

  @property({
    type: 'string',
  })
  apiUrl?: string;

  @property({
    type: 'string',
  })
  routeUrl?: string;

  @property({
    type: 'string',
  })
  severityLevel?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  parentId?: string;

  @belongsTo(() => Dropdown)
  dropdownId: string;

  constructor(data?: Partial<DropdownItems>) {
    super(data);
  }
}

export interface DropdownItemsRelations {
  // describe navigational properties here
}

export type DropdownItemsWithRelations = DropdownItems & DropdownItemsRelations;
