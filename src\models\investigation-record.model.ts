import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Incident} from './incident.model';

@model()
export class InvestigationRecord extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  documents?: string[];

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => Incident)
  incidentId: string;

  constructor(data?: Partial<InvestigationRecord>) {
    super(data);
  }
}

export interface InvestigationRecordRelations {
  // describe navigational properties here
}

export type InvestigationRecordWithRelations = InvestigationRecord & InvestigationRecordRelations;
