import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {ToolboxSignStatus, ToolboxTalk} from '../models';
import {ToolboxTalkRepository, UserRepository} from '../repositories';

const MASK_NAME = 'TBT';

@authenticate('cognito-jwt')
export class ToolboxTalkController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
  ) { }

  @post('/toolbox-talks')
  @response(200, {
    description: 'ToolboxTalk created with sign statuses',
    content: {'application/json': {schema: getModelSchemaRef(ToolboxTalk)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['toolboxTalk'],
            properties: {
              toolboxTalk: getModelSchemaRef(ToolboxTalk, {
                title: 'NewToolboxTalk',
                exclude: ['id'],
              }),
              toolboxSignStatuses: {
                type: 'array',
                items: getModelSchemaRef(ToolboxSignStatus, {
                  title: 'NewToolboxSignStatus',
                  exclude: ['id', 'toolboxTalkId'],
                }),
              },
            },
          },
        },
      },
    })
    requestData: {
      toolboxTalk: Omit<ToolboxTalk, 'id'>,
      toolboxSignStatuses?: Omit<ToolboxSignStatus, 'id' | 'toolboxTalkId'>[]
    },
  ): Promise<ToolboxTalk> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }
    const {toolboxTalk, toolboxSignStatuses} = requestData;

    // Generate unique maskId
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const count = await this.toolboxTalkRepository.count();
    const prefix = MASK_NAME; // replace with your actual constant or string

    toolboxTalk.maskId = `${prefix}-${year}${month}${day}-${count.count + 1}`;
    if (!user.id) {
      throw new HttpErrors.InternalServerError('User ID is undefined');
    }
    toolboxTalk.submittedById = user.id;
    // Step 1: Create the ToolboxTalk
    const createdToolboxTalk = await this.toolboxTalkRepository.create(toolboxTalk);

    // Step 2: Create related toolboxSignStatuses using the created toolboxTalk id
    if (toolboxSignStatuses?.length) {
      const signStatusRepo = this.toolboxTalkRepository.toolboxSignStatuses(createdToolboxTalk.id);

      await Promise.all(
        toolboxSignStatuses.map(status => signStatusRepo.create({
          ...status,
          toolboxTalkId: createdToolboxTalk.id,
        }))
      );
    }

    return createdToolboxTalk;
  }

  @get('/my-toolbox-talks')
  @response(200, {
    description: 'Array of ToolboxTalk model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ToolboxTalk, {includeRelations: true}),
        },
      },
    },
  })
  async findMy(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(ToolboxTalk) filter?: Filter<ToolboxTalk>,
  ): Promise<ToolboxTalk[]> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({where: {email: email}});
    if (!user) {
      throw new HttpErrors.NotFound(`User not found with this email: ${email}`);
    }


    return this.toolboxTalkRepository.find({...filter, where: {...filter?.where, submittedById: user.id}});
  }

  @get('/toolbox-talks/count')
  @response(200, {
    description: 'ToolboxTalk model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ToolboxTalk) where?: Where<ToolboxTalk>,
  ): Promise<Count> {
    return this.toolboxTalkRepository.count(where);
  }

  @get('/toolbox-talks')
  @response(200, {
    description: 'Array of ToolboxTalk model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ToolboxTalk, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ToolboxTalk) filter?: Filter<ToolboxTalk>,
  ): Promise<ToolboxTalk[]> {
    return this.toolboxTalkRepository.find(filter);
  }

  @patch('/toolbox-talks')
  @response(200, {
    description: 'ToolboxTalk PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxTalk, {partial: true}),
        },
      },
    })
    toolboxTalk: ToolboxTalk,
    @param.where(ToolboxTalk) where?: Where<ToolboxTalk>,
  ): Promise<Count> {
    return this.toolboxTalkRepository.updateAll(toolboxTalk, where);
  }

  @get('/toolbox-talks/{id}')
  @response(200, {
    description: 'ToolboxTalk model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ToolboxTalk, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ToolboxTalk, {exclude: 'where'}) filter?: FilterExcludingWhere<ToolboxTalk>
  ): Promise<ToolboxTalk> {
    return this.toolboxTalkRepository.findById(id, filter);
  }

  @patch('/toolbox-talks/{id}')
  @response(204, {
    description: 'ToolboxTalk PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxTalk, {partial: true}),
        },
      },
    })
    toolboxTalk: ToolboxTalk,
  ): Promise<void> {
    await this.toolboxTalkRepository.updateById(id, toolboxTalk);
  }

  @put('/toolbox-talks/{id}')
  @response(204, {
    description: 'ToolboxTalk PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() toolboxTalk: ToolboxTalk,
  ): Promise<void> {
    await this.toolboxTalkRepository.replaceById(id, toolboxTalk);
  }

  @del('/toolbox-talks/{id}')
  @response(204, {
    description: 'ToolboxTalk DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.toolboxTalkRepository.deleteById(id);
  }
}
