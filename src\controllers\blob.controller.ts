import {inject} from '@loopback/core';
import {post, requestBody, Response, RestBindings} from '@loopback/rest';
import axios from 'axios';

export class BlobController {
  constructor() { }

  @post('/get-blob', {
    responses: {
      '200': {
        description: 'Blob response',
        content: {
          'application/octet-stream': {
            schema: {
              type: 'string',
              format: 'binary',
            },
          },
        },
      },
    },
  })
  async getBlob(
    @requestBody({
      description: 'Request body containing the pre-signed URL',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              presignedUrl: {type: 'string'},
            },
            required: ['presignedUrl'],
          },
        },
      },
    })
    body: {presignedUrl: string},
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<void> {
    const {presignedUrl} = body;

    if (!presignedUrl) {
      response.status(400).send({error: 'No presigned URL provided'});
      return;
    }

    try {
      const axiosResponse = await axios.get(presignedUrl, {
        responseType: 'arraybuffer', // Fetch binary data
      });

      // Set response headers and content type
      response
        .status(200)
        .contentType(axiosResponse.headers['content-type'] || 'application/octet-stream')
        .send(axiosResponse.data);
    } catch (error) {
      console.error('Error fetching blob:', error.message);
      response.status(500).send({error: error.message});
    }
  }
}
