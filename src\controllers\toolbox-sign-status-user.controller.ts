import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxSignStatus,
  User,
} from '../models';
import {ToolboxSignStatusRepository} from '../repositories';

export class ToolboxSignStatusUserController {
  constructor(
    @repository(ToolboxSignStatusRepository)
    public toolboxSignStatusRepository: ToolboxSignStatusRepository,
  ) { }

  @get('/toolbox-sign-statuses/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to ToolboxSignStatus',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof ToolboxSignStatus.prototype.id,
  ): Promise<User> {
    return this.toolboxSignStatusRepository.signedBy(id);
  }
}
