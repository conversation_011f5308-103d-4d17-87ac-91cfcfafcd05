import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  WorkActivity,
  Department,
} from '../models';
import {WorkActivityRepository} from '../repositories';

export class WorkActivityDepartmentController {
  constructor(
    @repository(WorkActivityRepository)
    public workActivityRepository: WorkActivityRepository,
  ) { }

  @get('/work-activities/{id}/department', {
    responses: {
      '200': {
        description: 'Department belonging to WorkActivity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Department),
          },
        },
      },
    },
  })
  async getDepartment(
    @param.path.string('id') id: typeof WorkActivity.prototype.id,
  ): Promise<Department> {
    return this.workActivityRepository.department(id);
  }
}
