import {Entity, model, property} from '@loopback/repository';

@model()
export class EquipmentCategory extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  constructor(data?: Partial<EquipmentCategory>) {
    super(data);
  }
}

export interface EquipmentCategoryRelations {
  // describe navigational properties here
}

export type EquipmentCategoryWithRelations = EquipmentCategory & EquipmentCategoryRelations;
