import {Entity, model, property} from '@loopback/repository';

@model()
export class Hazards extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  image?: string;

  @property({
    type: 'string',
  })
  createdDate?: string;

  @property({
    type: 'string',
  })
  hazardsCategoryId?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  @property({
    type: 'string',
  })
  hazardCategoryId?: string;

  constructor(data?: Partial<Hazards>) {
    super(data);
  }
}

export interface HazardsRelations {
  // describe navigational properties here
}

export type HazardsWithRelations = Hazards & HazardsRelations;
