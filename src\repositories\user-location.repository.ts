import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {User, UserLocation, UserLocationRelations} from '../models';
import {UserRepository} from './user.repository';

export class UserLocationRepository extends DefaultCrudRepository<
  UserLocation,
  typeof UserLocation.prototype.id,
  UserLocationRelations
> {

  public readonly user: BelongsToAccessor<User, typeof UserLocation.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(UserLocation, dataSource);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
    this.user = this.createBelongsToAccessorFor('user', userRepositoryGetter,);
    this.registerInclusionResolver('user', this.user.inclusionResolver);
  }
}
