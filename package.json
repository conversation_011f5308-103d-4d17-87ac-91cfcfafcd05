{"name": "az-superadmin-api-v1", "version": "0.0.1", "description": "API for superadmin portal", "keywords": ["loopback-application", "loopback"], "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": "18 || 20 || 22"}, "scripts": {"build": "lb-tsc", "build:watch": "lb-tsc --watch", "lint": "npm run eslint && npm run prettier:check", "lint:fix": "npm run eslint:fix && npm run prettier:fix", "prettier:cli": "lb-prettier \"**/*.ts\" \"**/*.js\"", "prettier:check": "npm run prettier:cli -- -l", "prettier:fix": "npm run prettier:cli -- --write", "eslint": "lb-eslint --report-unused-disable-directives .", "eslint:fix": "npm run eslint -- --fix", "pretest": "npm run rebuild", "test": "lb-mocha --allow-console-logs \"dist/__tests__\"", "posttest": "npm run lint", "test:dev": "lb-mocha --allow-console-logs dist/__tests__/**/*.js && npm run posttest", "docker:build": "docker build -t az-superadmin-api-v1 .", "docker:run": "docker run -p 3000:3000 -d az-superadmin-api-v1", "premigrate": "npm run build", "migrate": "node ./dist/migrate", "preopenapi-spec": "npm run build", "openapi-spec": "node ./dist/openapi-spec", "prestart": "npm run rebuild", "start": "node -r source-map-support/register .", "clean": "lb-clean dist *.tsbuildinfo .eslintcache", "rebuild": "npm run clean && npm run build"}, "repository": {"type": "git", "url": ""}, "author": "adhi1 <<EMAIL>>", "license": "", "files": ["README.md", "dist", "src", "!*/__tests__"], "dependencies": {"@aws-sdk/client-s3": "^3.620.0", "@aws-sdk/lib-storage": "^3.620.0", "@aws-sdk/s3-request-presigner": "^3.620.0", "@loopback/authentication": "^11.0.4", "@loopback/authentication-jwt": "^0.15.4", "@loopback/authorization": "^0.15.4", "@loopback/boot": "^7.0.2", "@loopback/core": "^6.0.2", "@loopback/repository": "^7.0.4", "@loopback/rest": "^14.0.2", "@loopback/rest-crud": "^0.18.4", "@loopback/rest-explorer": "^7.0.2", "@loopback/security": "^0.11.4", "@loopback/service-proxy": "^7.0.2", "@types/multer": "^1.4.11", "aws-jwt-verify": "^4.0.1", "aws-sdk": "^2.1668.0", "axios": "^1.7.2", "dotenv": "^16.4.5", "install": "^0.13.0", "jose": "^5.6.3", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "loopback-connector-mongodb": "^6.2.0", "moment": "^2.30.1", "mongodb": "^6.8.0", "multer": "^1.4.5-lts.1", "npm": "^10.8.2", "pdf2docx": "^0.0.0", "tslib": "^2.0.0", "uuid": "^10.0.0"}, "devDependencies": {"@loopback/build": "^11.0.2", "@loopback/eslint-config": "^15.0.2", "@loopback/testlab": "^7.0.2", "@types/node": "^16.18.96", "@types/uuid": "^10.0.0", "eslint": "^8.57.0", "source-map-support": "^0.5.21", "typescript": "~5.2.2"}}