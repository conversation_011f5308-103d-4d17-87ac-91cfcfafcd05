import {Entity, hasMany, model, property} from '@loopback/repository';
import {LocationFive} from './location-five.model';

@model()
export class LocationFour extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  locationThreeId?: string;

  @hasMany(() => LocationFive)
  locationFives: LocationFive[];

  constructor(data?: Partial<LocationFour>) {
    super(data);
  }
}

export interface LocationFourRelations {
  // describe navigational properties here
}

export type LocationFourWithRelations = LocationFour & LocationFourRelations;
