import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Department} from './department.model';

@model()
export class WorkActivity extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => Department)
  departmentId: string;

  constructor(data?: Partial<WorkActivity>) {
    super(data);
  }
}

export interface WorkActivityRelations {
  // describe navigational properties here
}

export type WorkActivityWithRelations = WorkActivity & WorkActivityRelations;
