import {Model, model, property} from '@loopback/repository';

@model()
export class RelatedOrganizationalFactor extends Model {

  @property({
    type: 'string',
  })
  factor?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  extentOfContribution?: string;

  constructor(data?: Partial<RelatedOrganizationalFactor>) {
    super(data);
  }
}

export interface RelatedOrganizationalFactorRelations {
  // describe navigational properties here
}

export type RelatedOrganizationalFactorWithRelations = RelatedOrganizationalFactor & RelatedOrganizationalFactorRelations;
