import {Getter, inject} from '@loopback/core';
import {<PERSON>ongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, HasOneRepositoryFactory, juggler, repository} from '@loopback/repository';
import {DropdownItems, Incident, IncidentRelations, Investigation, InvestigationRecord, LocationFive, LocationFour, LocationOne, LocationSix, LocationThree, LocationTwo, User, WorkActivity, NearTermControlMeasures} from '../models';
import {DropdownItemsRepository} from './dropdown-items.repository';
import {InvestigationRecordRepository} from './investigation-record.repository';
import {InvestigationRepository} from './investigation.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationSixRepository} from './location-six.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationTwoRepository} from './location-two.repository';
import {UserRepository} from './user.repository';
import {WorkActivityRepository} from './work-activity.repository';
import {NearTermControlMeasuresRepository} from './near-term-control-measures.repository';

export class IncidentRepository extends DefaultCrudRepository<
  Incident,
  typeof Incident.prototype.id,
  IncidentRelations
> {

  public readonly investigationRecords: HasManyRepositoryFactory<InvestigationRecord, typeof Incident.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof Incident.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof Incident.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof Incident.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof Incident.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof Incident.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof Incident.prototype.id>;

  public readonly workActivity: BelongsToAccessor<WorkActivity, typeof Incident.prototype.id>;

  public readonly investigation: HasOneRepositoryFactory<Investigation, typeof Incident.prototype.id>;

  public readonly reportedBy: BelongsToAccessor<User, typeof Incident.prototype.id>;

  public readonly investigator: BelongsToAccessor<User, typeof Incident.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof Incident.prototype.id>;

  public readonly personnel: BelongsToAccessor<DropdownItems, typeof Incident.prototype.id>;

  public readonly environment: BelongsToAccessor<DropdownItems, typeof Incident.prototype.id>;

  public readonly property: BelongsToAccessor<DropdownItems, typeof Incident.prototype.id>;

  public readonly operation: BelongsToAccessor<DropdownItems, typeof Incident.prototype.id>;

  public readonly nearTermControlMeasures: HasManyRepositoryFactory<NearTermControlMeasures, typeof Incident.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('InvestigationRecordRepository') protected investigationRecordRepositoryGetter: Getter<InvestigationRecordRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('DropdownItemsRepository') protected dropdownItemsRepositoryGetter: Getter<DropdownItemsRepository>, @repository.getter('WorkActivityRepository') protected workActivityRepositoryGetter: Getter<WorkActivityRepository>, @repository.getter('InvestigationRepository') protected investigationRepositoryGetter: Getter<InvestigationRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('NearTermControlMeasuresRepository') protected nearTermControlMeasuresRepositoryGetter: Getter<NearTermControlMeasuresRepository>,
  ) {
    super(Incident, dataSource);
    this.nearTermControlMeasures = this.createHasManyRepositoryFactoryFor('nearTermControlMeasures', nearTermControlMeasuresRepositoryGetter,);
    this.registerInclusionResolver('nearTermControlMeasures', this.nearTermControlMeasures.inclusionResolver);
    this.operation = this.createBelongsToAccessorFor('operation', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('operation', this.operation.inclusionResolver);
    this.property = this.createBelongsToAccessorFor('property', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('property', this.property.inclusionResolver);
    this.environment = this.createBelongsToAccessorFor('environment', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('environment', this.environment.inclusionResolver);
    this.personnel = this.createBelongsToAccessorFor('personnel', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('personnel', this.personnel.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.investigator = this.createBelongsToAccessorFor('investigator', userRepositoryGetter,);
    this.registerInclusionResolver('investigator', this.investigator.inclusionResolver);
    this.reportedBy = this.createBelongsToAccessorFor('reportedBy', userRepositoryGetter,);
    this.registerInclusionResolver('reportedBy', this.reportedBy.inclusionResolver);
    this.investigation = this.createHasOneRepositoryFactoryFor('investigation', investigationRepositoryGetter);
    this.registerInclusionResolver('investigation', this.investigation.inclusionResolver);
    this.workActivity = this.createBelongsToAccessorFor('workActivity', workActivityRepositoryGetter,);
    this.registerInclusionResolver('workActivity', this.workActivity.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.investigationRecords = this.createHasManyRepositoryFactoryFor('investigationRecords', investigationRecordRepositoryGetter,);
    this.registerInclusionResolver('investigationRecords', this.investigationRecords.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
