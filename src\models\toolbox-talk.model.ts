import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {CloseOutChallenges} from './close-out-challenges.model';
import {LocationFive} from './location-five.model';
import {LocationFour} from './location-four.model';
import {LocationOne} from './location-one.model';
import {LocationSix} from './location-six.model';
import {LocationThree} from './location-three.model';
import {LocationTwo} from './location-two.model';
import {PermitRiskControl} from './permit-risk-control.model';
import {RiskAssessment} from './risk-assessment.model';
import {ToolboxAdditionalControl} from './toolbox-additional-control.model';
import {ToolboxSignStatus} from './toolbox-sign-status.model';
import {User} from './user.model';

@model()
export class ToolboxTalk extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'date',
  })
  commenceDate?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'number',
  })
  noOfPersonsParticipated?: number;

  @property({
    type: 'any',


  })
  tasks?: any;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(PermitRiskControl)

  })
  permitRiskControl?: PermitRiskControl[];

  @property({
    type: 'object',
    itemType: ToolboxAdditionalControl,
    jsonSchema: getJsonSchema(ToolboxAdditionalControl)
  })
  controls?: ToolboxAdditionalControl;



  @property({
    type: 'boolean',
  })
  isCloseOutChallenges?: boolean;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(CloseOutChallenges)

  })
  closeOutChallenges?: CloseOutChallenges[];

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  status?: 'Commenced' | 'Submitted' | 'Revoked' | 'Archived';

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @belongsTo(() => User)
  conductedById: string;

  @hasMany(() => ToolboxSignStatus)
  toolboxSignStatuses: ToolboxSignStatus[];

  @belongsTo(() => RiskAssessment)
  riskAssessmentId: string;

  @belongsTo(() => User)
  submittedById: string;

  constructor(data?: Partial<ToolboxTalk>) {
    super(data);
  }
}

export interface ToolboxTalkRelations {
  // describe navigational properties here
}

export type ToolboxTalkWithRelations = ToolboxTalk & ToolboxTalkRelations;
