import {Entity, model, property} from '@loopback/repository';

@model()
export class LocationSix extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  @property({
    type: 'string',
  })
  locationFiveId?: string;

  constructor(data?: Partial<LocationSix>) {
    super(data);
  }
}

export interface LocationSixRelations {
  // describe navigational properties here
}

export type LocationSixWithRelations = LocationSix & LocationSixRelations;
