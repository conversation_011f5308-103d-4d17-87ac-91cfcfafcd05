import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  RiskAssessment,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkRiskAssessmentController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/risk-assessment', {
    responses: {
      '200': {
        description: 'RiskAssessment belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(RiskAssessment),
          },
        },
      },
    },
  })
  async getRiskAssessment(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<RiskAssessment> {
    return this.toolboxTalkRepository.riskAssessment(id);
  }
}
