import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {DropdownItems} from './dropdown-items.model';
import {OttTask} from './ott-task.model';
import {User} from './user.model';

@model()
export class Ott extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  taskName?: string;

  @property({
    type: 'string',
  })
  taskDescription?: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  img?: string[];

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  assingneeComments?: string;

  @property({
    type: 'number',
  })
  estimatedPercentage?: number;

  @property({
    type: 'string',
  })
  priority?: 'High' | 'Medium' | 'Low';

  @property({
    type: 'string',
  })
  status?: 'Yet to Start' | 'Planning' | 'In Progress: On Track' | 'At Risk' | 'On Hold' | 'Under Review' | 'Testing / QA' | 'Ready for Deployment' | 'Completed' | 'Archived' | 'Returned';

  @property({
    type: 'string',
  })
  overallStatus?: 'Upcoming' | 'Due Soon' | 'Active' | 'Overdue';

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'boolean',
  })
  isArchive?: boolean;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  dueDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  reviewerComments?: string;

  @property({
    type: 'string',
  })
  archiveComments?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  isTaskIsCompletedForArchive?: boolean;

  @property({
    type: 'array',
    itemType: 'string'
  })
  docs?: string[];

  @property({
    type: 'boolean',
    default: true,
  })
  active?: boolean;

  @belongsTo(() => User)
  creatorId: string;

  @belongsTo(() => User)
  assigneeId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @hasMany(() => OttTask)
  ottTasks: OttTask[];

  @belongsTo(() => DropdownItems)
  projectId: string;

  @belongsTo(() => DropdownItems)
  categoryId: string;

  constructor(data?: Partial<Ott>) {
    super(data);
  }
}

export interface OttRelations {
  // describe navigational properties here
}

export type OttWithRelations = Ott & OttRelations;
