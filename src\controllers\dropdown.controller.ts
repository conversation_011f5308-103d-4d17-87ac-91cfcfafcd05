import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Dropdown} from '../models';
import {DropdownRepository} from '../repositories';

export class DropdownController {
  constructor(
    @repository(DropdownRepository)
    public dropdownRepository : DropdownRepository,
  ) {}

  @post('/dropdowns')
  @response(200, {
    description: 'Dropdown model instance',
    content: {'application/json': {schema: getModelSchemaRef(Dropdown)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Dropdown, {
            title: 'NewDropdown',
            exclude: ['id'],
          }),
        },
      },
    })
    dropdown: Omit<Dropdown, 'id'>,
  ): Promise<Dropdown> {
    return this.dropdownRepository.create(dropdown);
  }

  @get('/dropdowns/count')
  @response(200, {
    description: 'Dropdown model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Dropdown) where?: Where<Dropdown>,
  ): Promise<Count> {
    return this.dropdownRepository.count(where);
  }

  @get('/dropdowns')
  @response(200, {
    description: 'Array of Dropdown model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Dropdown, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Dropdown) filter?: Filter<Dropdown>,
  ): Promise<Dropdown[]> {
    return this.dropdownRepository.find(filter);
  }

  @patch('/dropdowns')
  @response(200, {
    description: 'Dropdown PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Dropdown, {partial: true}),
        },
      },
    })
    dropdown: Dropdown,
    @param.where(Dropdown) where?: Where<Dropdown>,
  ): Promise<Count> {
    return this.dropdownRepository.updateAll(dropdown, where);
  }

  @get('/dropdowns/{id}')
  @response(200, {
    description: 'Dropdown model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Dropdown, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Dropdown, {exclude: 'where'}) filter?: FilterExcludingWhere<Dropdown>
  ): Promise<Dropdown> {
    return this.dropdownRepository.findById(id, filter);
  }

  @patch('/dropdowns/{id}')
  @response(204, {
    description: 'Dropdown PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Dropdown, {partial: true}),
        },
      },
    })
    dropdown: Dropdown,
  ): Promise<void> {
    await this.dropdownRepository.updateById(id, dropdown);
  }

  @put('/dropdowns/{id}')
  @response(204, {
    description: 'Dropdown PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() dropdown: Dropdown,
  ): Promise<void> {
    await this.dropdownRepository.replaceById(id, dropdown);
  }

  @del('/dropdowns/{id}')
  @response(204, {
    description: 'Dropdown DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.dropdownRepository.deleteById(id);
  }
}
