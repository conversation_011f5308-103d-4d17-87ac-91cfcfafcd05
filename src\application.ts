import {AuthenticationComponent, registerAuthenticationStrategy} from '@loopback/authentication';
import {
  JWTAuthenticationComponent
} from '@loopback/authentication-jwt';
import {AuthorizationComponent} from '@loopback/authorization';
import {BootMixin} from '@loopback/boot';
import {ApplicationConfig, BindingScope} from '@loopback/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication} from '@loopback/rest';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import {ServiceMixin} from '@loopback/service-proxy';
import multer from 'multer';
import path from 'path';
import {FILE_UPLOAD_SERVICE, STORAGE_DIRECTORY} from './keys';
import {DynamicDataSourceProvider} from './providers/dynamic-datasource.provider';
import {MySequence} from './sequence';
import {ConfigService} from './services/config.service';
import {LocationFilterService} from './services/location-filter.service';
import {CognitoJwtAuthenticationStrategy} from './strategies/cognito-jwt-strategy';
export {ApplicationConfig};


export class AzSuperadminApiV1Application extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    super(options);

    // Bind authentication component related elements
    this.component(AuthenticationComponent);
    this.component(JWTAuthenticationComponent);
    this.component(AuthorizationComponent);




    registerAuthenticationStrategy(this, CognitoJwtAuthenticationStrategy);
    // Set up the custom sequence
    this.sequence(MySequence);
    this.bind('datasources.config.dynamic').toProvider(DynamicDataSourceProvider).inScope(BindingScope.REQUEST);

    // Set up default home page
    this.bind('services.ConfigService').toClass(ConfigService);
    this.static('/', path.join(__dirname, '../public'));

    this.service(LocationFilterService)
    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });
    this.component(RestExplorerComponent);

    this.configureFileUpload(options.fileStorageDirectory);
    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };
    // this.component(CrudRestComponent);
  }



  protected configureFileUpload(destination?: string) {
    // Upload files to `dist/.sandbox` by default
    destination = destination ?? path.join(__dirname, '../tmp/docs');
    this.bind(STORAGE_DIRECTORY).to(destination);
    const multerOptions: multer.Options = {
      storage: multer.diskStorage({
        destination,
        // Use the original file name as is
        filename: (req, file, cb) => {
          cb(null, new Date().getTime() + file.originalname);
        },
      }),
    };
    // Configure the file upload service with multer options
    this.configure(FILE_UPLOAD_SERVICE).to(multerOptions);
  }
}
