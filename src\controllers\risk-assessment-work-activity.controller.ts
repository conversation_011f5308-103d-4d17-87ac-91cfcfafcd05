import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RiskAssessment,
  WorkActivity,
} from '../models';
import {RiskAssessmentRepository} from '../repositories';

export class RiskAssessmentWorkActivityController {
  constructor(
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @get('/risk-assessments/{id}/work-activity', {
    responses: {
      '200': {
        description: 'WorkActivity belonging to RiskAssessment',
        content: {
          'application/json': {
            schema: getModelSchemaRef(WorkActivity),
          },
        },
      },
    },
  })
  async getWorkActivity(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
  ): Promise<WorkActivity> {
    return this.riskAssessmentRepository.workActivity(id);
  }
}
