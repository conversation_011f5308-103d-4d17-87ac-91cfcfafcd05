import {belongsTo, Entity, model, property} from '@loopback/repository';
import {User} from './user.model';

@model()
export class Checklist extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  version?: string;

  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
    jsonSchema: {
      enum: ['Draft', 'Published', 'Archived'],
    },
  })
  status?: 'Draft' | 'Published' | 'Archived';

  @property({
    type: 'boolean',
  })
  isArchive?: boolean;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'any',

  })
  value?: any;



  @property({
    type: 'string',
  })
  customId?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => User)
  curatorId: string;

  constructor(data?: Partial<Checklist>) {
    super(data);
  }
}

export interface ChecklistRelations {
  // describe navigational properties here
}

export type ChecklistWithRelations = Checklist & ChecklistRelations;
