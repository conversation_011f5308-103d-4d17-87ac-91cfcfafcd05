import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor} from '@loopback/repository';
import {DropdownItems, DropdownItemsRelations, Dropdown} from '../models';
import {DropdownRepository} from './dropdown.repository';

export class DropdownItemsRepository extends DefaultCrudRepository<
  DropdownItems,
  typeof DropdownItems.prototype.id,
  DropdownItemsRelations
> {

  public readonly dropdown: BelongsToAccessor<Dropdown, typeof DropdownItems.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('DropdownRepository') protected dropdownRepositoryGetter: Getter<DropdownRepository>,
  ) {
    super(DropdownItems, dataSource);
    this.dropdown = this.createBelongsToAccessorFor('dropdown', dropdownRepositoryGetter,);
    this.registerInclusionResolver('dropdown', this.dropdown.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
