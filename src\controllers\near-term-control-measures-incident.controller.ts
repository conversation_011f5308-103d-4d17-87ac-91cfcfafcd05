import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  NearTermControlMeasures,
  Incident,
} from '../models';
import {NearTermControlMeasuresRepository} from '../repositories';

export class NearTermControlMeasuresIncidentController {
  constructor(
    @repository(NearTermControlMeasuresRepository)
    public nearTermControlMeasuresRepository: NearTermControlMeasuresRepository,
  ) { }

  @get('/near-term-control-measures/{id}/incident', {
    responses: {
      '200': {
        description: 'Incident belonging to NearTermControlMeasures',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Incident),
          },
        },
      },
    },
  })
  async getIncident(
    @param.path.string('id') id: typeof NearTermControlMeasures.prototype.id,
  ): Promise<Incident> {
    return this.nearTermControlMeasuresRepository.incident(id);
  }
}
