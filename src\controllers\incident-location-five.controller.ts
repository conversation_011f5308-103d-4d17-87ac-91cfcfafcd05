import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  LocationFive,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentLocationFiveController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFive),
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<LocationFive> {
    return this.incidentRepository.locationFive(id);
  }
}
