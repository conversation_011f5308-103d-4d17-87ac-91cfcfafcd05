import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  WorkingGroup,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchWorkingGroupController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/working-group', {
    responses: {
      '200': {
        description: 'WorkingGroup belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(WorkingGroup),
          },
        },
      },
    },
  })
  async getWorkingGroup(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<WorkingGroup> {
    return this.goodCatchRepository.workingGroup(id);
  }
}
