// utils/passwordUtils.ts

const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
const numericChars = '0123456789';
const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

const requiredCharSets = [uppercaseChars, lowercaseChars, numericChars, specialChars];

export function generateRandomPassword(length: number = 12): string {
  const getRandomChar = (charset: string) => {
    const randomIndex = Math.floor(Math.random() * charset.length);
    return charset.charAt(randomIndex);
  };

  let password = '';

  // Ensure at least one character from each required character set
  requiredCharSets.forEach(charset => {
    password += getRandomChar(charset);
  });

  // Fill the remaining characters with random characters
  for (let i = requiredCharSets.length; i < length; i++) {
    const randomCharSet = requiredCharSets[Math.floor(Math.random() * requiredCharSets.length)];
    password += getRandomChar(randomCharSet);
  }

  // Shuffle the password characters to randomize the positions
  password = password.split('').sort(() => Math.random() - 0.5).join('');

  return password;
}
