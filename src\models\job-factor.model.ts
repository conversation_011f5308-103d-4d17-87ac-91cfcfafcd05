import {Entity, model, property} from '@loopback/repository';

@model()
export class JobFactor extends Entity {

  @property({
    type: 'string',
  })
  jobFactor?: string;

  @property({
    type: 'string',
  })
  fallibility?: string;

  @property({
    type: 'boolean',
  })
  isRoutine?: boolean;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  contributorFactor?: string;

  @property({
    type: 'string',
  })
  extentOfContribution?: "Significant" | "Insignificant" | "Major";



  constructor(data?: Partial<JobFactor>) {
    super(data);
  }
}

export interface JobFactorRelations {
  // describe navigational properties here
}

export type JobFactorWithRelations = JobFactor & JobFactorRelations;
