// Define a type for each control option within 'current_control' tasks
export interface ControlOption {
  value: string;
  files: string[];
  currentType: string; // Use union types if these values are fixed
  method: string; // Assuming method has specific allowed values, add more as needed
}

// Define a model for the high-risk hazard structure
export interface HighRiskHazard {
  hazardName: string;
  controls: ControlOption[]; // An array of ControlOption objects
  riskAssessmentId: string; // Assuming this is a string, adjust if it's a different type
}

// Example of response structure with a root model for handling multiple hazards
export interface HighRiskHazardResponse {
  hazards: HighRiskHazard[]; // Array of high-risk hazards
}
