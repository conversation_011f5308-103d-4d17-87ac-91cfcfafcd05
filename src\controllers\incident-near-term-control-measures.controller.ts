import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {
  Incident,
  NearTermControlMeasures,
} from '../models';
import {IncidentRepository, NearTermControlMeasuresRepository} from '../repositories';

@authenticate('cognito-jwt')
export class IncidentNearTermControlMeasuresController {
  constructor(
    @repository(IncidentRepository) protected incidentRepository: IncidentRepository,
    @repository(NearTermControlMeasuresRepository) protected nearTermControlMeasuresRepository: NearTermControlMeasuresRepository,
  ) { }

  @get('/incidents/{id}/near-term-control-measures', {
    responses: {
      '200': {
        description: 'Array of Incident has many NearTermControlMeasures',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NearTermControlMeasures)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<NearTermControlMeasures>,
  ): Promise<NearTermControlMeasures[]> {
    return this.incidentRepository.nearTermControlMeasures(id).find(filter);
  }

  @post('/incidents/{id}/near-term-control-measures', {
    responses: {
      '200': {
        description: 'Incident model instance',
        content: {'application/json': {schema: getModelSchemaRef(NearTermControlMeasures)}},
      },
    },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: typeof Incident.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NearTermControlMeasures, {
            title: 'NewNearTermControlMeasuresInIncident',
            exclude: ['id'],
            optional: ['incidentId']
          }),
        },
      },
    }) nearTermControlMeasures: Omit<NearTermControlMeasures, 'id'>,
  ): Promise<NearTermControlMeasures> {
    try {


      const incidentData = await this.incidentRepository.findById(id);
      const count = await this.nearTermControlMeasuresRepository.count({incidentId: id});

      nearTermControlMeasures.maskId = `${incidentData.maskId}-NTCM-${count.count + 1}`;
      nearTermControlMeasures.userId = currentUserProfile[securityId];
      nearTermControlMeasures.status = 'Yet to Start';

      return await this.incidentRepository.nearTermControlMeasures(id).create(nearTermControlMeasures);
    } catch (error) {
      // Handle different types of errors appropriately
      console.error(error);
      throw new HttpErrors.BadRequest(error.message);
    }

  }

  @patch('/incidents/{id}/near-term-control-measures', {
    responses: {
      '200': {
        description: 'Incident.NearTermControlMeasures PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NearTermControlMeasures, {partial: true}),
        },
      },
    })
    nearTermControlMeasures: Partial<NearTermControlMeasures>,
    @param.query.object('where', getWhereSchemaFor(NearTermControlMeasures)) where?: Where<NearTermControlMeasures>,
  ): Promise<Count> {
    return this.incidentRepository.nearTermControlMeasures(id).patch(nearTermControlMeasures, where);
  }

  @del('/incidents/{id}/near-term-control-measures', {
    responses: {
      '200': {
        description: 'Incident.NearTermControlMeasures DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(NearTermControlMeasures)) where?: Where<NearTermControlMeasures>,
  ): Promise<Count> {
    return this.incidentRepository.nearTermControlMeasures(id).delete(where);
  }
}
