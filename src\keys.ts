// Copyright IBM Corp. 2019,2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import {BindingKey as Key} from '@loopback/context';
import {FileUploadHandler} from './types';


/**
 * Binding key for the file upload service
 */
export const FILE_UPLOAD_SERVICE = Key.create<FileUploadHandler>(
  'services.FileUpload',
);

/**
 * Binding key for the storage directory
 */
export const STORAGE_DIRECTORY = Key.create<string>('storage.directory');
