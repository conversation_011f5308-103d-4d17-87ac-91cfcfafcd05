import {belongsTo, Entity, model, property} from '@loopback/repository';
import {RiskAssessment} from './risk-assessment.model';

@model()
export class RiskUpdate extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  reasonForReview?: string;

  @property({
    type: 'string',
  })
  changes?: string;

  @property({
    type: 'string',
  })
  reasonForChanges?: string;

  @property({
    type: 'string',
  })
  initiatedBy?: string;

  @property({
    type: 'string',
  })
  approvedBy?: string;

  @property({
    type: 'string',
  })
  reference?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  attachment?: string[];

  @property({
    type: 'object',
  })
  signature?: object;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @belongsTo(() => RiskAssessment)
  riskAssessmentId: string;

  constructor(data?: Partial<RiskUpdate>) {
    super(data);
  }
}

export interface RiskUpdateRelations {
  // describe navigational properties here
}

export type RiskUpdateWithRelations = RiskUpdate & RiskUpdateRelations;
