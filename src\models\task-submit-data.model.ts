import {Model, model, property} from '@loopback/repository';

@model()
export class TaskSubmitData extends Model {

  constructor(data?: Partial<TaskSubmitData>) {
    super(data);
  }

  @property({
    type: 'string',
    required: false,
    jsonSchema: {
      enum: ['Returned', 'Completed'],
    },
  })
  status?: 'Returned' | 'Completed';

  @property({
    type: 'string',
    required: false,
  })
  reviewerComments?: string;

  @property({
    type: 'string',
    required: false,
  })
  comments?: string;

  @property({
    type: 'string',
    required: false,
  })
  reviewerId?: string;

  @property({
    type: 'string',
    required: false,
  })
  actionTaken?: string;

  @property.array(String, {
    required: false,
  })
  evidence?: string[];
}

export interface TaskSubmitDataRelations {
  // describe navigational properties here
}

export type TaskSubmitDataWithRelations = TaskSubmitData & TaskSubmitDataRelations;
