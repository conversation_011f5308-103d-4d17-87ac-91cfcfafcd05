import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository, HasOneRepositoryFactory} from '@loopback/repository';
import {Department, RaTeamMember, RiskAssessment, RiskAssessmentRelations, RiskUpdate, User, WorkActivity, ToolboxTalk} from '../models';
import {DepartmentRepository} from './department.repository';
import {RaTeamMemberRepository} from './ra-team-member.repository';
import {RiskUpdateRepository} from './risk-update.repository';
import {UserRepository} from './user.repository';
import {WorkActivityRepository} from './work-activity.repository';
import {ToolboxTalkRepository} from './toolbox-talk.repository';

export class RiskAssessmentRepository extends DefaultCrudRepository<
  RiskAssessment,
  typeof RiskAssessment.prototype.id,
  RiskAssessmentRelations
> {

  public readonly riskUpdates: HasManyRepositoryFactory<RiskUpdate, typeof RiskAssessment.prototype.id>;

  public readonly teamLeader: BelongsToAccessor<User, typeof RiskAssessment.prototype.id>;

  public readonly raTeamMembers: HasManyRepositoryFactory<RaTeamMember, typeof RiskAssessment.prototype.id>;

  public readonly department: BelongsToAccessor<Department, typeof RiskAssessment.prototype.id>;

  public readonly workActivity: BelongsToAccessor<WorkActivity, typeof RiskAssessment.prototype.id>;

  public readonly toolboxTalk: HasOneRepositoryFactory<ToolboxTalk, typeof RiskAssessment.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('RiskUpdateRepository') protected riskUpdateRepositoryGetter: Getter<RiskUpdateRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('RaTeamMemberRepository') protected raTeamMemberRepositoryGetter: Getter<RaTeamMemberRepository>, @repository.getter('DepartmentRepository') protected departmentRepositoryGetter: Getter<DepartmentRepository>, @repository.getter('WorkActivityRepository') protected workActivityRepositoryGetter: Getter<WorkActivityRepository>, @repository.getter('ToolboxTalkRepository') protected toolboxTalkRepositoryGetter: Getter<ToolboxTalkRepository>,
  ) {

    super(RiskAssessment, dataSource);
    this.toolboxTalk = this.createHasOneRepositoryFactoryFor('toolboxTalk', toolboxTalkRepositoryGetter);
    this.registerInclusionResolver('toolboxTalk', this.toolboxTalk.inclusionResolver);
    console.log('RiskRepository instantiated');
    this.workActivity = this.createBelongsToAccessorFor('workActivity', workActivityRepositoryGetter,);
    this.registerInclusionResolver('workActivity', this.workActivity.inclusionResolver);
    this.department = this.createBelongsToAccessorFor('department', departmentRepositoryGetter,);
    this.registerInclusionResolver('department', this.department.inclusionResolver);
    this.raTeamMembers = this.createHasManyRepositoryFactoryFor('raTeamMembers', raTeamMemberRepositoryGetter,);
    this.registerInclusionResolver('raTeamMembers', this.raTeamMembers.inclusionResolver);
    this.teamLeader = this.createBelongsToAccessorFor('teamLeader', userRepositoryGetter,);
    this.registerInclusionResolver('teamLeader', this.teamLeader.inclusionResolver);
    this.riskUpdates = this.createHasManyRepositoryFactoryFor('riskUpdates', riskUpdateRepositoryGetter,);
    this.registerInclusionResolver('riskUpdates', this.riskUpdates.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
