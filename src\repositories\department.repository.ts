import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {Department, DepartmentRelations, WorkActivity} from '../models';
import {WorkActivityRepository} from './work-activity.repository';

export class DepartmentRepository extends DefaultCrudRepository<
  Department,
  typeof Department.prototype.id,
  DepartmentRelations
> {

  public readonly workActivities: HasManyRepositoryFactory<WorkActivity, typeof Department.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('WorkActivityRepository') protected workActivityRepositoryGetter: Getter<WorkActivityRepository>,
  ) {
    super(Department, dataSource);
    this.workActivities = this.createHasManyRepositoryFactoryFor('workActivities', workActivityRepositoryGetter,);
    this.registerInclusionResolver('workActivities', this.workActivities.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
