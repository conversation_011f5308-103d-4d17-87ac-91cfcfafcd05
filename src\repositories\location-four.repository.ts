import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {LocationFive, LocationFour, LocationFourRelations} from '../models';
import {LocationFiveRepository} from './location-five.repository';

export class LocationFourRepository extends DefaultCrudRepository<
  LocationFour,
  typeof LocationFour.prototype.id,
  LocationFourRelations
> {

  public readonly locationFives: HasManyRepositoryFactory<LocationFive, typeof LocationFour.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>,
  ) {
    super(LocationFour, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.locationFives = this.createHasManyRepositoryFactoryFor('locationFives', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFives', this.locationFives.inclusionResolver);
  }
}
