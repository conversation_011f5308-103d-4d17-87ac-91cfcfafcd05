import {Entity, hasMany, model, property} from '@loopback/repository';
import {LocationTwo} from './location-two.model';

@model()
export class LocationOne extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @hasMany(() => LocationTwo)
  locationTwos: LocationTwo[];

  constructor(data?: Partial<LocationOne>) {
    super(data);
  }
}

export interface LocationOneRelations {
  // describe navigational properties here
}

export type LocationOneWithRelations = LocationOne & LocationOneRelations;
