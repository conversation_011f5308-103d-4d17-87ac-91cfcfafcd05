import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, juggler, repository} from '@loopback/repository';
import {LocationOne, LocationOneRelations, LocationTwo} from '../models';
import {LocationTwoRepository} from './location-two.repository';

export class LocationOneRepository extends DefaultCrudRepository<
  LocationOne,
  typeof LocationOne.prototype.id,
  LocationOneRelations
> {

  public readonly locationTwos: HasManyRepositoryFactory<LocationTwo, typeof LocationOne.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>,
  ) {
    super(LocationOne, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.locationTwos = this.createHasManyRepositoryFactoryFor('locationTwos', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwos', this.locationTwos.inclusionResolver);
  }
}
