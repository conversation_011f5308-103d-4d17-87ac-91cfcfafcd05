import {Entity, model, property} from '@loopback/repository';

@model()
export class IncidentControl extends Entity {
  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'string',
  })
  controlStatement?: string;

  @property({
    type: 'boolean',
  })
  isControlImplemented?: boolean;

  @property({
    type: 'boolean',
  })
  isEffective?: boolean;


  constructor(data?: Partial<IncidentControl>) {
    super(data);
  }
}

export interface IncidentControlRelations {
  // describe navigational properties here
}

export type IncidentControlWithRelations = IncidentControl & IncidentControlRelations;
