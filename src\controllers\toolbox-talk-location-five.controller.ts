import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  LocationFive,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkLocationFiveController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFive),
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<LocationFive> {
    return this.toolboxTalkRepository.locationFive(id);
  }
}
