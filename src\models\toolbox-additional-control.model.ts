import {Entity, model, property} from '@loopback/repository';

@model()
export class ToolboxAdditionalControl extends Entity {
  @property({
    type: 'boolean',
    default: false,
  })
  isAdditionalControlsIdentified?: boolean;

  @property({
    type: 'string',
  })
  describeAdditionalControls?: string;

  @property({
    type: 'string',
  })
  teamBrief?: 'Yes' | 'No' | 'N/A';

  @property({
    type: 'string',
  })
  teamBriefRemarks?: string;


  constructor(data?: Partial<ToolboxAdditionalControl>) {
    super(data);
  }
}

export interface ToolboxAdditionalControlRelations {
  // describe navigational properties here
}

export type ToolboxAdditionalControlWithRelations = ToolboxAdditionalControl & ToolboxAdditionalControlRelations;
