import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  LocationSix,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentLocationSixController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationSix),
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<LocationSix> {
    return this.incidentRepository.locationSix(id);
  }
}
