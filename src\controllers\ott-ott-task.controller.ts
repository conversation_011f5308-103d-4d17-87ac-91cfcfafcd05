import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Ott,
  OttTask,
} from '../models';
import {OttRepository} from '../repositories';

export class OttOttTaskController {
  constructor(
    @repository(OttRepository) protected ottRepository: OttRepository,
  ) { }

  @get('/otts/{id}/ott-tasks', {
    responses: {
      '200': {
        description: 'Array of Ott has many OttTask',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(OttTask)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<OttTask>,
  ): Promise<OttTask[]> {
    return this.ottRepository.ottTasks(id).find(filter);
  }

  @post('/otts/{id}/ott-tasks', {
    responses: {
      '200': {
        description: 'Ott model instance',
        content: {'application/json': {schema: getModelSchemaRef(OttTask)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Ott.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OttTask, {
            title: 'NewOttTaskInOtt',
            exclude: ['id'],
            optional: ['ottId']
          }),
        },
      },
    }) ottTask: Omit<OttTask, 'id'>,
  ): Promise<OttTask> {
    return this.ottRepository.ottTasks(id).create(ottTask);
  }

  @patch('/otts/{id}/ott-tasks', {
    responses: {
      '200': {
        description: 'Ott.OttTask PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OttTask, {partial: true}),
        },
      },
    })
    ottTask: Partial<OttTask>,
    @param.query.object('where', getWhereSchemaFor(OttTask)) where?: Where<OttTask>,
  ): Promise<Count> {
    return this.ottRepository.ottTasks(id).patch(ottTask, where);
  }

  @del('/otts/{id}/ott-tasks', {
    responses: {
      '200': {
        description: 'Ott.OttTask DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(OttTask)) where?: Where<OttTask>,
  ): Promise<Count> {
    return this.ottRepository.ottTasks(id).delete(where);
  }
}
