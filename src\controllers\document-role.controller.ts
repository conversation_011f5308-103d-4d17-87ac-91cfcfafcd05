import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DocumentRole} from '../models';
import {DocumentRoleRepository} from '../repositories';

export class DocumentRoleController {
  constructor(
    @repository(DocumentRoleRepository)
    public documentRoleRepository : DocumentRoleRepository,
  ) {}

  @post('/document-roles')
  @response(200, {
    description: 'DocumentRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(DocumentRole)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentRole, {
            title: 'NewDocumentRole',
            exclude: ['id'],
          }),
        },
      },
    })
    documentRole: Omit<DocumentRole, 'id'>,
  ): Promise<DocumentRole> {
    return this.documentRoleRepository.create(documentRole);
  }

  @get('/document-roles/count')
  @response(200, {
    description: 'DocumentRole model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DocumentRole) where?: Where<DocumentRole>,
  ): Promise<Count> {
    return this.documentRoleRepository.count(where);
  }

  @get('/document-roles')
  @response(200, {
    description: 'Array of DocumentRole model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DocumentRole, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DocumentRole) filter?: Filter<DocumentRole>,
  ): Promise<DocumentRole[]> {
    return this.documentRoleRepository.find(filter);
  }

  @patch('/document-roles')
  @response(200, {
    description: 'DocumentRole PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentRole, {partial: true}),
        },
      },
    })
    documentRole: DocumentRole,
    @param.where(DocumentRole) where?: Where<DocumentRole>,
  ): Promise<Count> {
    return this.documentRoleRepository.updateAll(documentRole, where);
  }

  @get('/document-roles/{id}')
  @response(200, {
    description: 'DocumentRole model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DocumentRole, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DocumentRole, {exclude: 'where'}) filter?: FilterExcludingWhere<DocumentRole>
  ): Promise<DocumentRole> {
    return this.documentRoleRepository.findById(id, filter);
  }

  @patch('/document-roles/{id}')
  @response(204, {
    description: 'DocumentRole PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DocumentRole, {partial: true}),
        },
      },
    })
    documentRole: DocumentRole,
  ): Promise<void> {
    await this.documentRoleRepository.updateById(id, documentRole);
  }

  @put('/document-roles/{id}')
  @response(204, {
    description: 'DocumentRole PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() documentRole: DocumentRole,
  ): Promise<void> {
    await this.documentRoleRepository.replaceById(id, documentRole);
  }

  @del('/document-roles/{id}')
  @response(204, {
    description: 'DocumentRole DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.documentRoleRepository.deleteById(id);
  }
}
