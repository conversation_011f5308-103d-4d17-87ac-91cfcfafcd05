import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ToolboxTalk,
  ToolboxSignStatus,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkToolboxSignStatusController {
  constructor(
    @repository(ToolboxTalkRepository) protected toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/toolbox-sign-statuses', {
    responses: {
      '200': {
        description: 'Array of ToolboxTalk has many ToolboxSignStatus',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ToolboxSignStatus)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ToolboxSignStatus>,
  ): Promise<ToolboxSignStatus[]> {
    return this.toolboxTalkRepository.toolboxSignStatuses(id).find(filter);
  }

  @post('/toolbox-talks/{id}/toolbox-sign-statuses', {
    responses: {
      '200': {
        description: 'ToolboxTalk model instance',
        content: {'application/json': {schema: getModelSchemaRef(ToolboxSignStatus)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxSignStatus, {
            title: 'NewToolboxSignStatusInToolboxTalk',
            exclude: ['id'],
            optional: ['toolboxTalkId']
          }),
        },
      },
    }) toolboxSignStatus: Omit<ToolboxSignStatus, 'id'>,
  ): Promise<ToolboxSignStatus> {
    return this.toolboxTalkRepository.toolboxSignStatuses(id).create(toolboxSignStatus);
  }

  @patch('/toolbox-talks/{id}/toolbox-sign-statuses', {
    responses: {
      '200': {
        description: 'ToolboxTalk.ToolboxSignStatus PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxSignStatus, {partial: true}),
        },
      },
    })
    toolboxSignStatus: Partial<ToolboxSignStatus>,
    @param.query.object('where', getWhereSchemaFor(ToolboxSignStatus)) where?: Where<ToolboxSignStatus>,
  ): Promise<Count> {
    return this.toolboxTalkRepository.toolboxSignStatuses(id).patch(toolboxSignStatus, where);
  }

  @del('/toolbox-talks/{id}/toolbox-sign-statuses', {
    responses: {
      '200': {
        description: 'ToolboxTalk.ToolboxSignStatus DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ToolboxSignStatus)) where?: Where<ToolboxSignStatus>,
  ): Promise<Count> {
    return this.toolboxTalkRepository.toolboxSignStatuses(id).delete(where);
  }
}
