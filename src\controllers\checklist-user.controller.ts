import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Checklist,
  User,
} from '../models';
import {ChecklistRepository} from '../repositories';

export class ChecklistUserController {
  constructor(
    @repository(ChecklistRepository)
    public checklistRepository: ChecklistRepository,
  ) { }

  @get('/checklists/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Checklist',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Checklist.prototype.id,
  ): Promise<User> {
    return this.checklistRepository.curator(id);
  }
}
