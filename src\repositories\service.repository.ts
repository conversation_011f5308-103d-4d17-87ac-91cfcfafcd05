import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, HasManyThroughRepositoryFactory, juggler, repository} from '@loopback/repository';
import {Role, Service, ServiceRelations, Tenant, TenantService, Dropdown, Action} from '../models';
import {RoleRepository} from './role.repository';
import {TenantServiceRepository} from './tenant-service.repository';
import {TenantRepository} from './tenant.repository';
import {DropdownRepository} from './dropdown.repository';
import {ActionRepository} from './action.repository';

export class ServiceRepository extends DefaultCrudRepository<
  Service,
  typeof Service.prototype.id,
  ServiceRelations
> {

  public readonly roles: HasManyRepositoryFactory<Role, typeof Service.prototype.id>;

  public readonly tenants: HasManyThroughRepositoryFactory<Tenant, typeof Tenant.prototype.id,
    TenantService,
    typeof Service.prototype.id
  >;

  public readonly dropdowns: HasManyRepositoryFactory<Dropdown, typeof Service.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof Service.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('RoleRepository') protected roleRepositoryGetter: Getter<RoleRepository>, @repository.getter('TenantServiceRepository') protected tenantServiceRepositoryGetter: Getter<TenantServiceRepository>, @repository.getter('TenantRepository') protected tenantRepositoryGetter: Getter<TenantRepository>, @repository.getter('DropdownRepository') protected dropdownRepositoryGetter: Getter<DropdownRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>,
  ) {
    super(Service, dataSource);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.dropdowns = this.createHasManyRepositoryFactoryFor('dropdowns', dropdownRepositoryGetter,);
    this.registerInclusionResolver('dropdowns', this.dropdowns.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.tenants = this.createHasManyThroughRepositoryFactoryFor('tenants', tenantRepositoryGetter, tenantServiceRepositoryGetter,);
    this.registerInclusionResolver('tenants', this.tenants.inclusionResolver);
    this.roles = this.createHasManyRepositoryFactoryFor('roles', roleRepositoryGetter,);
    this.registerInclusionResolver('roles', this.roles.inclusionResolver);
  }
}
