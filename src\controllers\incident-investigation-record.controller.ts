import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Incident,
  InvestigationRecord,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentInvestigationRecordController {
  constructor(
    @repository(IncidentRepository) protected incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/investigation-records', {
    responses: {
      '200': {
        description: 'Array of Incident has many InvestigationRecord',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(InvestigationRecord)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<InvestigationRecord>,
  ): Promise<InvestigationRecord[]> {
    return this.incidentRepository.investigationRecords(id).find(filter);
  }

  @post('/incidents/{id}/investigation-records', {
    responses: {
      '200': {
        description: 'Incident model instance',
        content: {'application/json': {schema: getModelSchemaRef(InvestigationRecord)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Incident.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecord, {
            title: 'NewInvestigationRecordInIncident',
            exclude: ['id'],
            optional: ['incidentId']
          }),
        },
      },
    }) investigationRecord: Omit<InvestigationRecord, 'id'>,
  ): Promise<InvestigationRecord> {
    return this.incidentRepository.investigationRecords(id).create(investigationRecord);
  }

  @patch('/incidents/{id}/investigation-records', {
    responses: {
      '200': {
        description: 'Incident.InvestigationRecord PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecord, {partial: true}),
        },
      },
    })
    investigationRecord: Partial<InvestigationRecord>,
    @param.query.object('where', getWhereSchemaFor(InvestigationRecord)) where?: Where<InvestigationRecord>,
  ): Promise<Count> {
    return this.incidentRepository.investigationRecords(id).patch(investigationRecord, where);
  }

  @del('/incidents/{id}/investigation-records', {
    responses: {
      '200': {
        description: 'Incident.InvestigationRecord DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(InvestigationRecord)) where?: Where<InvestigationRecord>,
  ): Promise<Count> {
    return this.incidentRepository.investigationRecords(id).delete(where);
  }
}
