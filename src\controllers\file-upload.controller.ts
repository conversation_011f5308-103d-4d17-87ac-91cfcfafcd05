import {S3Client} from "@aws-sdk/client-s3";
import {Upload} from "@aws-sdk/lib-storage";
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {post, Request, requestBody, Response, RestBindings} from '@loopback/rest';
import {promises as fs} from 'fs';
import path from 'path';
import {FILE_UPLOAD_SERVICE} from '../keys';
import {ConfigRepository} from '../repositories';
import {FileUploadHandler} from '../types';



// import {authenticate} from '@loopback/authentication';

// @authenticate('cognito-jwt')
export class FileUploadController {
  constructor(
    @repository(ConfigRepository)
    public configRepository: ConfigRepository,
    @inject(FILE_UPLOAD_SERVICE) private handler: FileUploadHandler,
  ) { }

  async uploadFileToS3(file: any) {

    const cognitoRegion = await this.configRepository.getConfigValue('COGNITO_REGION');
    const awsAccessKeyId = await this.configRepository.getConfigValue('AWS_ACCESS_KEY_ID');
    const awsSecretAccessKey = await this.configRepository.getConfigValue('AWS_SECRET_ACCESS_KEY');

    const awsS3BucketName = await this.configRepository.getConfigValue('AWS_S3_BUCKET_NAME');

    const s3 = new S3Client({
      region: `${cognitoRegion}`,
      credentials: {
        accessKeyId: `${awsAccessKeyId}`,
        secretAccessKey: `${awsSecretAccessKey}`,
      },
    });

    const BUCKET_NAME = `${awsS3BucketName}`;

    const filePath = path.join(file.destination, file.originalname);
    const fileBuffer = await fs.readFile(filePath);

    const upload = new Upload({
      client: s3,
      params: {
        Bucket: BUCKET_NAME,
        Key: file.originalname,
        Body: fileBuffer,
        ContentType: file.mimetype,
      },
    });

    try {

      const data = await upload.done();
      await fs.unlink(filePath)
      return data;
    } catch (err) {
      console.error('Error uploading file to S3:', err);
      throw err;
    }
  }

  @post('/files', {
    responses: {
      200: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
            },
          },
        },
        description: 'Files and fields',
      },
    },
  })
  async fileUpload(
    @requestBody.file() request: Request,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<object> {
    return new Promise<object>((resolve, reject) => {
      this.handler(request, response, async (err: unknown) => {
        if (err) reject(err);
        else {
          const {files} = FileUploadController.getFilesAndFields(request);

          for (const file of files) {

            await this.uploadFileToS3(file);
          }
          resolve(FileUploadController.getFilesAndFields(request));
        }
      });
    });
  }

  private static getFilesAndFields(request: Request) {
    const uploadedFiles = request.files;
    const mapper = (f: globalThis.Express.Multer.File) => ({
      fieldname: f.fieldname,
      originalname: f.filename,
      encoding: f.encoding,
      mimetype: f.mimetype,
      size: f.size,
      destination: f.destination, // Add destination to the file object
    });

    let files: object[] = [];
    if (Array.isArray(uploadedFiles)) {
      files = uploadedFiles.map(mapper);
    } else {
      for (const filename in uploadedFiles) {
        files.push(...uploadedFiles[filename].map(mapper));
      }
    }
    return {files, fields: request.body};
  }
}
