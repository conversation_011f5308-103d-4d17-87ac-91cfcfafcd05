import {belongsTo, Entity, hasMany, hasOne, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {Action} from './action.model';
import {Consequence} from './consequence.model';
import {DropdownItems} from './dropdown-items.model';
import {Hazard} from './hazard.model';
import {ImmediateCause} from './immediate-cause.model';
import {InvestigationRecord} from './investigation-record.model';
import {Investigation} from './investigation.model';
import {LocationFive} from './location-five.model';
import {LocationFour} from './location-four.model';
import {LocationOne} from './location-one.model';
import {LocationSix} from './location-six.model';
import {LocationThree} from './location-three.model';
import {LocationTwo} from './location-two.model';
import {MitigativeControl} from './mitigative-control.model';
import {NearTermControlMeasures} from './near-term-control-measures.model';
import {Precursor} from './precursor.model';
import {PreventiveControl} from './preventive-control.model';
import {User} from './user.model';
import {WorkActivity} from './work-activity.model';

@model()
export class Incident extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'boolean',
  })
  isInvestigationRequired?: boolean;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  images?: string[];

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  personnelInvolved?: string;

  @property({
    type: 'string',
  })
  witnessInvolved?: string;

  @property({
    type: 'boolean',
  })
  isCustomLocation?: boolean;

  @property({
    type: 'string',
  })
  customLocation?: string;

  @property({
    type: 'string',
  })
  immediateActionsTaken?: string;


  @property({
    type: 'object',
    itemType: Consequence,
    jsonSchema: getJsonSchema(Consequence)
  })
  consequences?: Consequence;

  @property({
    type: 'object',
    itemType: ImmediateCause,
    jsonSchema: getJsonSchema(ImmediateCause)
  })
  immediateCause?: ImmediateCause;

  @property({
    type: 'object',
    itemType: Precursor,
    jsonSchema: getJsonSchema(Precursor)
  })
  precursors?: Precursor;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Hazard)

  })
  hazards?: Hazard[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Action)

  })
  totalActions?: Action[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(Action)

  })
  completedActions?: Action[];

  @property({
    type: 'string',
  })
  hazardousSituationOfTheIncident?: string;

  @property({
    type: 'string',


  })
  hazardStatus?: "Draft" | "Completed";

  @property({
    type: 'string',
  })
  nearTermControlMeasureStatus?: "Draft" | "Completed";

  @property({
    type: 'object',
    itemType: PreventiveControl,
    jsonSchema: getJsonSchema(PreventiveControl)
  })
  preventiveControls?: PreventiveControl;

  @property({
    type: 'object',
    itemType: MitigativeControl,
    jsonSchema: getJsonSchema(MitigativeControl)
  })
  mitigativeControls?: MitigativeControl;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  incidentDate?: string;

  @property({
    type: 'string',
  })
  incidentStatus?: "Draft" | "Completed" | "Under Review" | "Under Investigation" | "Archived";

  @property({
    type: 'string',
  })
  status?: "Draft" | "Completed" | "Under Review" | "Under Investigation" | "Investigation Completed" | "Archived" | "Reviewed";

  @hasMany(() => InvestigationRecord)
  investigationRecords: InvestigationRecord[];

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @property({
    type: 'string',
  })
  incidentType?: string;

  @property({
    type: 'string',
  }) incidentCategory?: string;


  @property({
    type: 'string',
  })
  incidentCircumstance?: string;

  @property({
    type: 'string',
  })
  surfaceType?: string;


  @property({
    type: 'string',
  })
  surfaceCondition?: string;

  @property({
    type: 'string',
  })
  pathways?: string;


  @property({
    type: 'string',
  })
  lighting?: string;



  @belongsTo(() => WorkActivity)
  workActivityId: string;

  @property({
    type: 'string',
  })
  weatherCondition?: string;



  @hasOne(() => Investigation)
  investigation: Investigation;

  @belongsTo(() => User)
  reportedById: string;

  @belongsTo(() => User)
  investigatorId: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => DropdownItems)
  personnelId: string;

  @belongsTo(() => DropdownItems)
  environmentId: string;

  @belongsTo(() => DropdownItems)
  propertyId: string;

  @belongsTo(() => DropdownItems)
  operationId: string;

  @hasMany(() => NearTermControlMeasures)
  nearTermControlMeasures: NearTermControlMeasures[];

  constructor(data?: Partial<Incident>) {
    super(data);
  }
}

export interface IncidentRelations {
  // describe navigational properties here
}

export type IncidentWithRelations = Incident & IncidentRelations;

