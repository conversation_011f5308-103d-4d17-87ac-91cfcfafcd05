import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor} from '@loopback/repository';
import {InvestigationRecommendation, InvestigationRecommendationRelations, User} from '../models';
import {UserRepository} from './user.repository';

export class InvestigationRecommendationRepository extends DefaultCrudRepository<
  InvestigationRecommendation,
  typeof InvestigationRecommendation.prototype.id,
  InvestigationRecommendationRelations
> {

  public readonly assignedTo: BelongsToAccessor<User, typeof InvestigationRecommendation.prototype.id>;

  public readonly submittedBy: BelongsToAccessor<User, typeof InvestigationRecommendation.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof InvestigationRecommendation.prototype.id>;

  public readonly approver: BelongsToAccessor<User, typeof InvestigationRecommendation.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(InvestigationRecommendation, dataSource);
    this.approver = this.createBelongsToAccessorFor('approver', userRepositoryGetter,);
    this.registerInclusionResolver('approver', this.approver.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.submittedBy = this.createBelongsToAccessorFor('submittedBy', userRepositoryGetter,);
    this.registerInclusionResolver('submittedBy', this.submittedBy.inclusionResolver);
    this.assignedTo = this.createBelongsToAccessorFor('assignedTo', userRepositoryGetter,);
    this.registerInclusionResolver('assignedTo', this.assignedTo.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
