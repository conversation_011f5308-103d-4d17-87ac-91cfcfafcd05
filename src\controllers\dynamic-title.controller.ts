import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DynamicTitle} from '../models';
import {DynamicTitleRepository} from '../repositories';

export class DynamicTitleController {
  constructor(
    @repository(DynamicTitleRepository)
    public dynamicTitleRepository : DynamicTitleRepository,
  ) {}

  @post('/dynamic-titles')
  @response(200, {
    description: 'DynamicTitle model instance',
    content: {'application/json': {schema: getModelSchemaRef(DynamicTitle)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DynamicTitle, {
            title: 'NewDynamicTitle',
            exclude: ['id'],
          }),
        },
      },
    })
    dynamicTitle: Omit<DynamicTitle, 'id'>,
  ): Promise<DynamicTitle> {
    return this.dynamicTitleRepository.create(dynamicTitle);
  }

  @get('/dynamic-titles/count')
  @response(200, {
    description: 'DynamicTitle model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DynamicTitle) where?: Where<DynamicTitle>,
  ): Promise<Count> {
    return this.dynamicTitleRepository.count(where);
  }

  @get('/dynamic-titles')
  @response(200, {
    description: 'Array of DynamicTitle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DynamicTitle, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DynamicTitle) filter?: Filter<DynamicTitle>,
  ): Promise<DynamicTitle[]> {
    return this.dynamicTitleRepository.find(filter);
  }

  @patch('/dynamic-titles')
  @response(200, {
    description: 'DynamicTitle PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DynamicTitle, {partial: true}),
        },
      },
    })
    dynamicTitle: DynamicTitle,
    @param.where(DynamicTitle) where?: Where<DynamicTitle>,
  ): Promise<Count> {
    return this.dynamicTitleRepository.updateAll(dynamicTitle, where);
  }

  @get('/dynamic-titles/{id}')
  @response(200, {
    description: 'DynamicTitle model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DynamicTitle, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DynamicTitle, {exclude: 'where'}) filter?: FilterExcludingWhere<DynamicTitle>
  ): Promise<DynamicTitle> {
    return this.dynamicTitleRepository.findById(id, filter);
  }

  @patch('/dynamic-titles/{id}')
  @response(204, {
    description: 'DynamicTitle PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DynamicTitle, {partial: true}),
        },
      },
    })
    dynamicTitle: DynamicTitle,
  ): Promise<void> {
    await this.dynamicTitleRepository.updateById(id, dynamicTitle);
  }

  @put('/dynamic-titles/{id}')
  @response(204, {
    description: 'DynamicTitle PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() dynamicTitle: DynamicTitle,
  ): Promise<void> {
    await this.dynamicTitleRepository.replaceById(id, dynamicTitle);
  }

  @del('/dynamic-titles/{id}')
  @response(204, {
    description: 'DynamicTitle DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.dynamicTitleRepository.deleteById(id);
  }
}
