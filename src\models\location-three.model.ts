import {Entity, hasMany, model, property} from '@loopback/repository';
import {LocationFour} from './location-four.model';

@model()
export class LocationThree extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  @property({
    type: 'string',
  })
  locationTwoId?: string;

  @hasMany(() => LocationFour)
  locationFours: LocationFour[];

  constructor(data?: Partial<LocationThree>) {
    super(data);
  }
}

export interface LocationThreeRelations {
  // describe navigational properties here
}

export type LocationThreeWithRelations = LocationThree & LocationThreeRelations;
