import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Investigation,
  Incident,
} from '../models';
import {InvestigationRepository} from '../repositories';

export class InvestigationIncidentController {
  constructor(
    @repository(InvestigationRepository)
    public investigationRepository: InvestigationRepository,
  ) { }

  @get('/investigations/{id}/incident', {
    responses: {
      '200': {
        description: 'Incident belonging to Investigation',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Incident),
          },
        },
      },
    },
  })
  async getIncident(
    @param.path.string('id') id: typeof Investigation.prototype.id,
  ): Promise<Incident> {
    return this.investigationRepository.incident(id);
  }
}
