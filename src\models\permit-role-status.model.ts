import {Entity, model, property} from '@loopback/repository';

@model()
export class PermitRoleStatus extends Entity {
  @property({
    type: 'string',
  })
  signature?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  status?: boolean;

  @property({
    type: 'date',
  })
  signedDate?: string;

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  attachments?: string[];


  constructor(data?: Partial<PermitRoleStatus>) {
    super(data);
  }
}

export interface PermitRoleStatusRelations {
  // describe navigational properties here
}

export type PermitRoleStatusWithRelations = PermitRoleStatus & PermitRoleStatusRelations;
