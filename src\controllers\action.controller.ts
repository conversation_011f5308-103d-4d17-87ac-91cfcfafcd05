import {authenticate} from '@loopback/authentication';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {Action} from '../models';
import {ActionRepository, OttRepository, PermitReportRepository} from '../repositories';

@authenticate('cognito-jwt')
export class ActionController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(OttRepository)
    public ottRepository: OttRepository,
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @post('/actions')
  @response(200, {
    description: 'Action model instance',
    content: {'application/json': {schema: getModelSchemaRef(Action)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {
            title: 'NewAction',
            exclude: ['id'],
          }),
        },
      },
    })
    action: Omit<Action, 'id'>,
  ): Promise<Action> {
    return this.actionRepository.create(action);
  }

  @get('/actions/count')
  @response(200, {
    description: 'Action model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Action) where?: Where<Action>,
  ): Promise<Count> {
    return this.actionRepository.count(where);
  }


  @get('/my-submitted-actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, {includeRelations: true}),
        },
      },
    },
  })
  async findMySubmittedActions(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<Action[]> {

    return this.actionRepository.find(

      {
        ...filter,
        where: {
          and: [
            {submittedById: currentUserProfile[securityId]},
            {status: 'Initiated'}
          ]


        }
      });
  }


  @get('/my-assigned-actions/{application}')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, {includeRelations: true}),
        },
      },
    },
  })
  async findMyAssignedActions(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('application') application?: string,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<Action[]> {


    const whereCondition: Where = {
      and: [
        {
          assignedToId: {
            inq: [[currentUserProfile[securityId]]]
          }
        },
        {status: 'Initiated'},

      ]
    }

    if (application && application !== 'All') {
      whereCondition.and.push({application: application})
    }
    const actions = await this.actionRepository.find(
      {
        ...filter,
        where: whereCondition
      });



    const enhancedActions = await Promise.all(actions.map(action => this.enhanceActionDetails(action, application)));

    return enhancedActions;
  }

  async enhanceActionDetails(action: Action, application: string | undefined): Promise<Action> {
    switch (application) {
      case 'OTT':
        {
          const ottDetails = await this.ottRepository.findById(action.applicationId, {include: [{relation: "creator"}, {relation: "assignee"}, {relation: "reviewer"}, {relation: "project"}]})
          action.applicationDetails = ottDetails;


          break;
        }

      case 'EPTW-GEN':
        {
          const eptwDetails = await this.permitReportRepository.findById(action.applicationId, {

            include: [
              {
                relation: "applicant",

              },
              {
                relation: "assessor",

              },
              {
                relation: "approver",

              },
              {
                relation: "reviewer",

              }
            ]
          });
          action.applicationDetails = eptwDetails;


          break;
        }
      default:
        action.applicationDetails = {extraInfo: 'General application information'};
    }
    return action;
  }

  @get('/v2/my-assigned-actions/{application}')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, {includeRelations: true}),
        },
      },
    },
  })
  async findMyAssignedActionsV2(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('application') application?: string,
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<Action[]> {


    const whereCondition: Where = {
      and: [
        {
          assignedToId: {
            inq: [[currentUserProfile[securityId]]]
          }
        },
        {status: 'Initiated'},

      ]
    }

    if (application && application !== 'All') {
      whereCondition.and.push({application: application})
    }
    const actions = await this.actionRepository.find(
      {
        ...filter,
        where: whereCondition
      });



    const enhancedActions = await Promise.all(actions.map(action => this.enhanceActionDetails(action, application)));

    return enhancedActions;
  }

  // async enhanceActionDetails(action: Action, application: string | undefined): Promise<Action> {
  //   switch (application) {
  //     case 'OTT':
  //       {
  //         const ottDetails = await this.ottRepository.findById(action.applicationId, {include: [{relation: "creator"}, {relation: "assignee"}, {relation: "reviewer"}, {relation: "project"}]})
  //         action.applicationDetails = ottDetails;


  //         break;
  //       }

  //     case 'EPTW-GEN':
  //       {
  //         const eptwDetails = await this.permitReportRepository.findById(action.applicationId, {

  //           include: [
  //             {
  //               relation: "applicant",

  //             },
  //             {
  //               relation: "assessor",

  //             },
  //             {
  //               relation: "approver",

  //             },
  //             {
  //               relation: "reviewer",

  //             }
  //           ]
  //         });
  //         action.applicationDetails = eptwDetails;


  //         break;
  //       }
  //     default:
  //       action.applicationDetails = {extraInfo: 'General application information'};
  //   }
  //   return action;
  // }

  @get('/my-actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, {includeRelations: true}),
        },
      },
    },
  })
  async findAll(
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.actionRepository.find(filter);
  }

  @get('/actions')
  @response(200, {
    description: 'Array of Action model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Action, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Action) filter?: Filter<Action>,
  ): Promise<Action[]> {
    return this.actionRepository.find(filter);
  }

  @patch('/actions')
  @response(200, {
    description: 'Action PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Action,
    @param.where(Action) where?: Where<Action>,
  ): Promise<Count> {
    return this.actionRepository.updateAll(action, where);
  }

  @get('/actions/{id}')
  @response(200, {
    description: 'Action model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Action, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Action, {exclude: 'where'}) filter?: FilterExcludingWhere<Action>
  ): Promise<Action> {
    return this.actionRepository.findById(id, filter);
  }

  @patch('/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    await this.actionRepository.updateById(id, action);
  }

  @put('/actions/{id}')
  @response(204, {
    description: 'Action PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() action: Action,
  ): Promise<void> {
    await this.actionRepository.replaceById(id, action);
  }

  @del('/actions/{id}')
  @response(204, {
    description: 'Action DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.actionRepository.deleteById(id);
  }
}
