import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {InvestigationRecord} from '../models';
import {InvestigationRecordRepository} from '../repositories';

export class InvestigationRecordController {
  constructor(
    @repository(InvestigationRecordRepository)
    public investigationRecordRepository : InvestigationRecordRepository,
  ) {}

  @post('/investigation-records')
  @response(200, {
    description: 'InvestigationRecord model instance',
    content: {'application/json': {schema: getModelSchemaRef(InvestigationRecord)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecord, {
            title: 'NewInvestigationRecord',
            exclude: ['id'],
          }),
        },
      },
    })
    investigationRecord: Omit<InvestigationRecord, 'id'>,
  ): Promise<InvestigationRecord> {
    return this.investigationRecordRepository.create(investigationRecord);
  }

  @get('/investigation-records/count')
  @response(200, {
    description: 'InvestigationRecord model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(InvestigationRecord) where?: Where<InvestigationRecord>,
  ): Promise<Count> {
    return this.investigationRecordRepository.count(where);
  }

  @get('/investigation-records')
  @response(200, {
    description: 'Array of InvestigationRecord model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(InvestigationRecord, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(InvestigationRecord) filter?: Filter<InvestigationRecord>,
  ): Promise<InvestigationRecord[]> {
    return this.investigationRecordRepository.find(filter);
  }

  @patch('/investigation-records')
  @response(200, {
    description: 'InvestigationRecord PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecord, {partial: true}),
        },
      },
    })
    investigationRecord: InvestigationRecord,
    @param.where(InvestigationRecord) where?: Where<InvestigationRecord>,
  ): Promise<Count> {
    return this.investigationRecordRepository.updateAll(investigationRecord, where);
  }

  @get('/investigation-records/{id}')
  @response(200, {
    description: 'InvestigationRecord model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(InvestigationRecord, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(InvestigationRecord, {exclude: 'where'}) filter?: FilterExcludingWhere<InvestigationRecord>
  ): Promise<InvestigationRecord> {
    return this.investigationRecordRepository.findById(id, filter);
  }

  @patch('/investigation-records/{id}')
  @response(204, {
    description: 'InvestigationRecord PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecord, {partial: true}),
        },
      },
    })
    investigationRecord: InvestigationRecord,
  ): Promise<void> {
    await this.investigationRecordRepository.updateById(id, investigationRecord);
  }

  @put('/investigation-records/{id}')
  @response(204, {
    description: 'InvestigationRecord PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() investigationRecord: InvestigationRecord,
  ): Promise<void> {
    await this.investigationRecordRepository.replaceById(id, investigationRecord);
  }

  @del('/investigation-records/{id}')
  @response(204, {
    description: 'InvestigationRecord DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.investigationRecordRepository.deleteById(id);
  }
}
