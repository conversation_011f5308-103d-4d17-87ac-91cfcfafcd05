import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MongodbDataSource} from '../datasources';
import {GoodCatch, GoodCatchRelations, User, Action, WorkingGroup, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix} from '../models';
import {UserRepository} from './user.repository';
import {ActionRepository} from './action.repository';
import {WorkingGroupRepository} from './working-group.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';

export class GoodCatchRepository extends DefaultCrudRepository<
  GoodCatch,
  typeof GoodCatch.prototype.id,
  GoodCatchRelations
> {

  public readonly reporter: BelongsToAccessor<User, typeof GoodCatch.prototype.id>;

  public readonly admin: BelongsToAccessor<User, typeof GoodCatch.prototype.id>;

  public readonly actionOwner: BelongsToAccessor<User, typeof GoodCatch.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof GoodCatch.prototype.id>;

  public readonly workingGroup: BelongsToAccessor<WorkingGroup, typeof GoodCatch.prototype.id>;

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof GoodCatch.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof GoodCatch.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof GoodCatch.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof GoodCatch.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof GoodCatch.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof GoodCatch.prototype.id>;

  constructor(
    @inject('datasources.mongodb') dataSource: MongodbDataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>, @repository.getter('WorkingGroupRepository') protected workingGroupRepositoryGetter: Getter<WorkingGroupRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>,
  ) {
    super(GoodCatch, dataSource);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);
    this.workingGroup = this.createBelongsToAccessorFor('workingGroup', workingGroupRepositoryGetter,);
    this.registerInclusionResolver('workingGroup', this.workingGroup.inclusionResolver);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.actionOwner = this.createBelongsToAccessorFor('actionOwner', userRepositoryGetter,);
    this.registerInclusionResolver('actionOwner', this.actionOwner.inclusionResolver);
    this.admin = this.createBelongsToAccessorFor('admin', userRepositoryGetter,);
    this.registerInclusionResolver('admin', this.admin.inclusionResolver);
    this.reporter = this.createBelongsToAccessorFor('reporter', userRepositoryGetter,);
    this.registerInclusionResolver('reporter', this.reporter.inclusionResolver);
  }
}
