import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  LocationFour,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionLocationFourController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to Inspection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<LocationFour> {
    return this.inspectionRepository.locationFour(id);
  }
}
