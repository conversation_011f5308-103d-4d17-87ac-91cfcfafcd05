import {Entity, model, property} from '@loopback/repository';

@model()
export class DocumentRole extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  permission?: any;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  constructor(data?: Partial<DocumentRole>) {
    super(data);
  }
}

export interface DocumentRoleRelations {
  // describe navigational properties here
}

export type DocumentRoleWithRelations = DocumentRole & DocumentRoleRelations;
