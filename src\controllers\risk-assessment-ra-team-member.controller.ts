import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  RiskAssessment,
  RaTeamMember,
} from '../models';
import {RiskAssessmentRepository} from '../repositories';

export class RiskAssessmentRaTeamMemberController {
  constructor(
    @repository(RiskAssessmentRepository) protected riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @get('/risk-assessments/{id}/ra-team-members', {
    responses: {
      '200': {
        description: 'Array of RiskAssessment has many RaTeamMember',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(RaTeamMember)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<RaTeamMember>,
  ): Promise<RaTeamMember[]> {
    return this.riskAssessmentRepository.raTeamMembers(id).find(filter);
  }

  @post('/risk-assessments/{id}/ra-team-members', {
    responses: {
      '200': {
        description: 'RiskAssessment model instance',
        content: {'application/json': {schema: getModelSchemaRef(RaTeamMember)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaTeamMember, {
            title: 'NewRaTeamMemberInRiskAssessment',
            exclude: ['id'],
            optional: ['riskAssessmentId']
          }),
        },
      },
    }) raTeamMember: Omit<RaTeamMember, 'id'>,
  ): Promise<RaTeamMember> {
    return this.riskAssessmentRepository.raTeamMembers(id).create(raTeamMember);
  }

  @patch('/risk-assessments/{id}/ra-team-members', {
    responses: {
      '200': {
        description: 'RiskAssessment.RaTeamMember PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RaTeamMember, {partial: true}),
        },
      },
    })
    raTeamMember: Partial<RaTeamMember>,
    @param.query.object('where', getWhereSchemaFor(RaTeamMember)) where?: Where<RaTeamMember>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.raTeamMembers(id).patch(raTeamMember, where);
  }

  @del('/risk-assessments/{id}/ra-team-members', {
    responses: {
      '200': {
        description: 'RiskAssessment.RaTeamMember DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(RaTeamMember)) where?: Where<RaTeamMember>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.raTeamMembers(id).delete(where);
  }
}
