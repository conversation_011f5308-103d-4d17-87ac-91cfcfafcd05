import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {ToolboxTalk, ToolboxTalkRelations, LocationOne, LocationTwo, LocationThree, LocationFour, LocationFive, LocationSix, User, ToolboxSignStatus, RiskAssessment} from '../models';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';
import {LocationThreeRepository} from './location-three.repository';
import {LocationFourRepository} from './location-four.repository';
import {LocationFiveRepository} from './location-five.repository';
import {LocationSixRepository} from './location-six.repository';
import {UserRepository} from './user.repository';
import {ToolboxSignStatusRepository} from './toolbox-sign-status.repository';
import {RiskAssessmentRepository} from './risk-assessment.repository';

export class ToolboxTalkRepository extends DefaultCrudRepository<
  ToolboxTalk,
  typeof ToolboxTalk.prototype.id,
  ToolboxTalkRelations
> {

  public readonly locationOne: BelongsToAccessor<LocationOne, typeof ToolboxTalk.prototype.id>;

  public readonly locationTwo: BelongsToAccessor<LocationTwo, typeof ToolboxTalk.prototype.id>;

  public readonly locationThree: BelongsToAccessor<LocationThree, typeof ToolboxTalk.prototype.id>;

  public readonly locationFour: BelongsToAccessor<LocationFour, typeof ToolboxTalk.prototype.id>;

  public readonly locationFive: BelongsToAccessor<LocationFive, typeof ToolboxTalk.prototype.id>;

  public readonly locationSix: BelongsToAccessor<LocationSix, typeof ToolboxTalk.prototype.id>;

  public readonly conductedBy: BelongsToAccessor<User, typeof ToolboxTalk.prototype.id>;

  public readonly toolboxSignStatuses: HasManyRepositoryFactory<ToolboxSignStatus, typeof ToolboxTalk.prototype.id>;

  public readonly riskAssessment: BelongsToAccessor<RiskAssessment, typeof ToolboxTalk.prototype.id>;

  public readonly submittedBy: BelongsToAccessor<User, typeof ToolboxTalk.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>, @repository.getter('LocationFourRepository') protected locationFourRepositoryGetter: Getter<LocationFourRepository>, @repository.getter('LocationFiveRepository') protected locationFiveRepositoryGetter: Getter<LocationFiveRepository>, @repository.getter('LocationSixRepository') protected locationSixRepositoryGetter: Getter<LocationSixRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('ToolboxSignStatusRepository') protected toolboxSignStatusRepositoryGetter: Getter<ToolboxSignStatusRepository>, @repository.getter('RiskAssessmentRepository') protected riskAssessmentRepositoryGetter: Getter<RiskAssessmentRepository>,
  ) {
    super(ToolboxTalk, dataSource);
    this.submittedBy = this.createBelongsToAccessorFor('submittedBy', userRepositoryGetter,);
    this.registerInclusionResolver('submittedBy', this.submittedBy.inclusionResolver);
    this.riskAssessment = this.createBelongsToAccessorFor('riskAssessment', riskAssessmentRepositoryGetter,);
    this.registerInclusionResolver('riskAssessment', this.riskAssessment.inclusionResolver);
    this.toolboxSignStatuses = this.createHasManyRepositoryFactoryFor('toolboxSignStatuses', toolboxSignStatusRepositoryGetter,);
    this.registerInclusionResolver('toolboxSignStatuses', this.toolboxSignStatuses.inclusionResolver);
    this.conductedBy = this.createBelongsToAccessorFor('conductedBy', userRepositoryGetter,);
    this.registerInclusionResolver('conductedBy', this.conductedBy.inclusionResolver);
    this.locationSix = this.createBelongsToAccessorFor('locationSix', locationSixRepositoryGetter,);
    this.registerInclusionResolver('locationSix', this.locationSix.inclusionResolver);
    this.locationFive = this.createBelongsToAccessorFor('locationFive', locationFiveRepositoryGetter,);
    this.registerInclusionResolver('locationFive', this.locationFive.inclusionResolver);
    this.locationFour = this.createBelongsToAccessorFor('locationFour', locationFourRepositoryGetter,);
    this.registerInclusionResolver('locationFour', this.locationFour.inclusionResolver);
    this.locationThree = this.createBelongsToAccessorFor('locationThree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThree', this.locationThree.inclusionResolver);
    this.locationTwo = this.createBelongsToAccessorFor('locationTwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('locationTwo', this.locationTwo.inclusionResolver);
    this.locationOne = this.createBelongsToAccessorFor('locationOne', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOne', this.locationOne.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
