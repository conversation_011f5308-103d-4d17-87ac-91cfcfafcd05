import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {LocationFour} from '../models';
import {LocationFourRepository} from '../repositories';

export class LocationFourController {
  constructor(
    @repository(LocationFourRepository)
    public locationFourRepository : LocationFourRepository,
  ) {}

  @post('/location-fours')
  @response(200, {
    description: 'LocationFour model instance',
    content: {'application/json': {schema: getModelSchemaRef(LocationFour)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {
            title: 'NewLocationFour',
            exclude: ['id'],
          }),
        },
      },
    })
    locationFour: Omit<LocationFour, 'id'>,
  ): Promise<LocationFour> {
    return this.locationFourRepository.create(locationFour);
  }

  @get('/location-fours/count')
  @response(200, {
    description: 'LocationFour model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(LocationFour) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationFourRepository.count(where);
  }

  @get('/location-fours')
  @response(200, {
    description: 'Array of LocationFour model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(LocationFour, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(LocationFour) filter?: Filter<LocationFour>,
  ): Promise<LocationFour[]> {
    return this.locationFourRepository.find(filter);
  }

  @patch('/location-fours')
  @response(200, {
    description: 'LocationFour PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {partial: true}),
        },
      },
    })
    locationFour: LocationFour,
    @param.where(LocationFour) where?: Where<LocationFour>,
  ): Promise<Count> {
    return this.locationFourRepository.updateAll(locationFour, where);
  }

  @get('/location-fours/{id}')
  @response(200, {
    description: 'LocationFour model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationFour, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(LocationFour, {exclude: 'where'}) filter?: FilterExcludingWhere<LocationFour>
  ): Promise<LocationFour> {
    return this.locationFourRepository.findById(id, filter);
  }

  @patch('/location-fours/{id}')
  @response(204, {
    description: 'LocationFour PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationFour, {partial: true}),
        },
      },
    })
    locationFour: LocationFour,
  ): Promise<void> {
    await this.locationFourRepository.updateById(id, locationFour);
  }

  @put('/location-fours/{id}')
  @response(204, {
    description: 'LocationFour PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() locationFour: LocationFour,
  ): Promise<void> {
    await this.locationFourRepository.replaceById(id, locationFour);
  }

  @del('/location-fours/{id}')
  @response(204, {
    description: 'LocationFour DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.locationFourRepository.deleteById(id);
  }
}
