import {Entity, model, property} from '@loopback/repository';

@model()
export class Designation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;
  constructor(data?: Partial<Designation>) {
    super(data);
  }
}

export interface DesignationRelations {
  // describe navigational properties here
}

export type DesignationWithRelations = Designation & DesignationRelations;
