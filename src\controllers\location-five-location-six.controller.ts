import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationFive,
  LocationSix,
} from '../models';
import {LocationFiveRepository} from '../repositories';

export class LocationFiveLocationSixController {
  constructor(
    @repository(LocationFiveRepository) protected locationFiveRepository: LocationFiveRepository,
  ) { }

  @get('/location-fives/{id}/location-sixes', {
    responses: {
      '200': {
        description: 'Array of LocationFive has many LocationSix',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationSix)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<LocationSix>,
  ): Promise<LocationSix[]> {
    return this.locationFiveRepository.locationSixes(id).find(filter);
  }

  @post('/location-fives/{id}/location-sixes', {
    responses: {
      '200': {
        description: 'LocationFive model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationSix)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof LocationFive.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationSix, {
            title: 'NewLocationSixInLocationFive',
            exclude: ['id'],
            optional: ['locationFiveId']
          }),
        },
      },
    }) locationSix: Omit<LocationSix, 'id'>,
  ): Promise<LocationSix> {
    return this.locationFiveRepository.locationSixes(id).create(locationSix);
  }

  @patch('/location-fives/{id}/location-sixes', {
    responses: {
      '200': {
        description: 'LocationFive.LocationSix PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationSix, {partial: true}),
        },
      },
    })
    locationSix: Partial<LocationSix>,
    @param.query.object('where', getWhereSchemaFor(LocationSix)) where?: Where<LocationSix>,
  ): Promise<Count> {
    return this.locationFiveRepository.locationSixes(id).patch(locationSix, where);
  }

  @del('/location-fives/{id}/location-sixes', {
    responses: {
      '200': {
        description: 'LocationFive.LocationSix DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(LocationSix)) where?: Where<LocationSix>,
  ): Promise<Count> {
    return this.locationFiveRepository.locationSixes(id).delete(where);
  }
}
