import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RiskAssessment,
  User,
} from '../models';
import {RiskAssessmentRepository} from '../repositories';

export class RiskAssessmentUserController {
  constructor(
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @get('/risk-assessments/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to RiskAssessment',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
  ): Promise<User> {
    return this.riskAssessmentRepository.teamLeader(id);
  }
}
