import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  LocationTwo,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchLocationTwoController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<LocationTwo> {
    return this.goodCatchRepository.locationTwo(id);
  }
}
