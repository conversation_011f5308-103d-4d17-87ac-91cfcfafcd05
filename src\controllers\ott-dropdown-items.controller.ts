import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Ott,
  DropdownItems,
} from '../models';
import {OttRepository} from '../repositories';

export class OttDropdownItemsController {
  constructor(
    @repository(OttRepository)
    public ottRepository: OttRepository,
  ) { }

  @get('/otts/{id}/dropdown-items', {
    responses: {
      '200': {
        description: 'DropdownItems belonging to <PERSON><PERSON>',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DropdownItems),
          },
        },
      },
    },
  })
  async getDropdownItems(
    @param.path.string('id') id: typeof Ott.prototype.id,
  ): Promise<DropdownItems> {
    return this.ottRepository.project(id);
  }
}
