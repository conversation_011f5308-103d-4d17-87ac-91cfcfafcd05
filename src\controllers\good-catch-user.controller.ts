import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  User,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchUserController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<User> {
    return this.goodCatchRepository.reporter(id);
  }
}
