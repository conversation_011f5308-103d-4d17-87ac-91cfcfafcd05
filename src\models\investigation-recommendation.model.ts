import {belongsTo, Entity, model, property} from '@loopback/repository';
import {User} from './user.model';

@model()
export class InvestigationRecommendation extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  recommendedActions?: string;

  @property({
    type: 'string',
  })
  rationaleForRecommendation?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'date',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  reviewerComments?: string;

  @property({
    type: 'string',
  })
  approverComments?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  status?: 'Yet to Start' | 'In Progress: On Track' | 'Under Review' | 'Completed' | 'Archived' | 'Returned';


  @belongsTo(() => User)
  assignedToId: string;

  @property({
    type: 'string',
  })
  investigationId?: string;

  @belongsTo(() => User)
  submittedById: string;

  @belongsTo(() => User)
  reviewerId: string;

  @belongsTo(() => User)
  approverId: string;

  constructor(data?: Partial<InvestigationRecommendation>) {
    super(data);
  }
}

export interface InvestigationRecommendationRelations {
  // describe navigational properties here
}

export type InvestigationRecommendationWithRelations = InvestigationRecommendation & InvestigationRecommendationRelations;
