import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {LocationSix} from '../models';
import {LocationSixRepository} from '../repositories';

export class LocationSixController {
  constructor(
    @repository(LocationSixRepository)
    public locationSixRepository : LocationSixRepository,
  ) {}

  @post('/location-sixes')
  @response(200, {
    description: 'LocationSix model instance',
    content: {'application/json': {schema: getModelSchemaRef(LocationSix)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationSix, {
            title: 'NewLocationSix',
            exclude: ['id'],
          }),
        },
      },
    })
    locationSix: Omit<LocationSix, 'id'>,
  ): Promise<LocationSix> {
    return this.locationSixRepository.create(locationSix);
  }

  @get('/location-sixes/count')
  @response(200, {
    description: 'LocationSix model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(LocationSix) where?: Where<LocationSix>,
  ): Promise<Count> {
    return this.locationSixRepository.count(where);
  }

  @get('/location-sixes')
  @response(200, {
    description: 'Array of LocationSix model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(LocationSix, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(LocationSix) filter?: Filter<LocationSix>,
  ): Promise<LocationSix[]> {
    return this.locationSixRepository.find(filter);
  }

  @patch('/location-sixes')
  @response(200, {
    description: 'LocationSix PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationSix, {partial: true}),
        },
      },
    })
    locationSix: LocationSix,
    @param.where(LocationSix) where?: Where<LocationSix>,
  ): Promise<Count> {
    return this.locationSixRepository.updateAll(locationSix, where);
  }

  @get('/location-sixes/{id}')
  @response(200, {
    description: 'LocationSix model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationSix, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(LocationSix, {exclude: 'where'}) filter?: FilterExcludingWhere<LocationSix>
  ): Promise<LocationSix> {
    return this.locationSixRepository.findById(id, filter);
  }

  @patch('/location-sixes/{id}')
  @response(204, {
    description: 'LocationSix PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationSix, {partial: true}),
        },
      },
    })
    locationSix: LocationSix,
  ): Promise<void> {
    await this.locationSixRepository.updateById(id, locationSix);
  }

  @put('/location-sixes/{id}')
  @response(204, {
    description: 'LocationSix PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() locationSix: LocationSix,
  ): Promise<void> {
    await this.locationSixRepository.replaceById(id, locationSix);
  }

  @del('/location-sixes/{id}')
  @response(204, {
    description: 'LocationSix DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.locationSixRepository.deleteById(id);
  }
}
