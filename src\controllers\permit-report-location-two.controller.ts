import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  PermitReport,
  LocationTwo,
} from '../models';
import {PermitReportRepository} from '../repositories';

export class PermitReportLocationTwoController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
  ) { }

  @get('/permit-reports/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to PermitReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof PermitReport.prototype.id,
  ): Promise<LocationTwo> {
    return this.permitReportRepository.locationTwo(id);
  }
}
