import {Entity, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {ConsequenceItem} from './consequence-item.model';

@model()
export class Consequence extends Entity {
  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(ConsequenceItem)

  })
  consequenceItems?: ConsequenceItem[];

  @property({
    type: 'string',
  })
  status?: "Draft" | "Completed";

  constructor(data?: Partial<Consequence>) {
    super(data);
  }
}

export interface ConsequenceRelations {
  // describe navigational properties here
}

export type ConsequenceWithRelations = Consequence & ConsequenceRelations;
