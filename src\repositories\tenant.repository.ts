import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyThroughRepositoryFactory, juggler, repository} from '@loopback/repository';
import {Service, Tenant, TenantRelations, TenantService} from '../models';
import {ServiceRepository} from './service.repository';
import {TenantServiceRepository} from './tenant-service.repository';

export class TenantRepository extends DefaultCrudRepository<
  Tenant,
  typeof Tenant.prototype.id,
  TenantRelations
> {

  public readonly services: HasManyThroughRepositoryFactory<Service, typeof Service.prototype.id,
    TenantService,
    typeof Tenant.prototype.id
  >;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('TenantServiceRepository') protected tenantServiceRepositoryGetter: Getter<TenantServiceRepository>, @repository.getter('ServiceRepository') protected serviceRepositoryGetter: Getter<ServiceRepository>,
  ) {
    super(Tenant, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });


    this.services = this.createHasManyThroughRepositoryFactoryFor('services', serviceRepositoryGetter, tenantServiceRepositoryGetter,);
    this.registerInclusionResolver('services', this.services.inclusionResolver);
  }
}
