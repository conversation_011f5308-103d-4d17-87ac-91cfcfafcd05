import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {Task} from '../models/task.model';
import {TaskRepository} from '../repositories/task.repository';

@injectable({scope: BindingScope.TRANSIENT})
export class TaskService {
  constructor(
    @repository(TaskRepository)
    private taskRepo: TaskRepository,
  ) { }

  async createTask(taskData: Partial<Task>): Promise<Task> {
    return this.taskRepo.create(taskData);
  }

  async updateTask(taskId: string, data: Partial<Task>): Promise<void> {
    await this.taskRepo.updateById(taskId, data);
  }

  async findById(taskId: string): Promise<Task> {
    return this.taskRepo.findById(taskId);
  }

  async findTasks(filter?: object): Promise<Task[]> {
    return this.taskRepo.find(filter);
  }

  async deleteTask(taskId: string): Promise<void> {
    await this.taskRepo.deleteById(taskId);
  }
}
