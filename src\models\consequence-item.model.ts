import {Entity, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {ConsequenceDescription} from './consequence-description.model';

@model()
export class ConsequenceItem extends Entity {
  @property({
    type: 'string',
  })
  impactOn?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(ConsequenceDescription)

  })
  consequenceDescription?: ConsequenceDescription[];


  constructor(data?: Partial<ConsequenceItem>) {
    super(data);
  }
}

export interface ConsequenceItemRelations {
  // describe navigational properties here
}

export type ConsequenceItemWithRelations = ConsequenceItem & ConsequenceItemRelations;
