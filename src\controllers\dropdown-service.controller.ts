import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Dropdown,
  Service,
} from '../models';
import {DropdownRepository} from '../repositories';

export class DropdownServiceController {
  constructor(
    @repository(DropdownRepository)
    public dropdownRepository: DropdownRepository,
  ) { }

  @get('/dropdowns/{id}/service', {
    responses: {
      '200': {
        description: 'Service belonging to Dropdown',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Service),
          },
        },
      },
    },
  })
  async getService(
    @param.path.string('id') id: typeof Dropdown.prototype.id,
  ): Promise<Service> {
    return this.dropdownRepository.service(id);
  }
}
