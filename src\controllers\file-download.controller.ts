import {GetObjectCommand, S3Client} from "@aws-sdk/client-s3";
import {getSignedUrl} from "@aws-sdk/s3-request-presigner";
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {get, param, Response, RestBindings} from '@loopback/rest';
import {ConfigRepository} from '../repositories';



// import {authenticate} from '@loopback/authentication';

// @authenticate('cognito-jwt')
export class FileDownloadController {
  constructor(
    @repository(ConfigRepository)
    public configRepository: ConfigRepository,
  ) { }

  @get('/files/{filename}/presigned-url', {
    responses: {
      200: {
        description: 'Get a pre-signed URL for downloading a file',
        content: {
          'text/plain': {
            schema: {
              type: 'string',
            },
          },
        },
      },
    },
  })
  async getPresignedUrl(
    @param.path.string('filename') filename: string,
    @inject(RestBindings.Http.RESPONSE) response: Response,
  ): Promise<string> {

    const cognitoRegion = await this.configRepository.getConfigValue('COGNITO_REGION');
    const awsAccessKeyId = await this.configRepository.getConfigValue('AWS_ACCESS_KEY_ID');
    const awsSecretAccessKey = await this.configRepository.getConfigValue('AWS_SECRET_ACCESS_KEY');

    const awsS3BucketName = await this.configRepository.getConfigValue('AWS_S3_BUCKET_NAME');

    const s3 = new S3Client({
      region: `${cognitoRegion}`,
      credentials: {
        accessKeyId: `${awsAccessKeyId}`,
        secretAccessKey: `${awsSecretAccessKey}`,
      },
    });

    const BUCKET_NAME = `${awsS3BucketName}`;

    const getObjectParams = {
      Bucket: BUCKET_NAME,
      Key: filename,
    };

    try {
      const command = new GetObjectCommand(getObjectParams);
      const url = await getSignedUrl(s3, command, {expiresIn: 3600}); // URL expires in 1 hour

      return url;
    } catch (err) {
      console.error('Error generating pre-signed URL:', err);
      response.status(500).send({error: 'Error generating pre-signed URL'});
      return '';
    }
  }
}

