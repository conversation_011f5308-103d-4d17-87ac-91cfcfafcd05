import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  NearTermControlMeasures,
  User,
} from '../models';
import {NearTermControlMeasuresRepository} from '../repositories';

export class NearTermControlMeasuresUserController {
  constructor(
    @repository(NearTermControlMeasuresRepository)
    public nearTermControlMeasuresRepository: NearTermControlMeasuresRepository,
  ) { }

  @get('/near-term-control-measures/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to NearTermControlMeasures',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof NearTermControlMeasures.prototype.id,
  ): Promise<User> {
    return this.nearTermControlMeasuresRepository.user(id);
  }
}
