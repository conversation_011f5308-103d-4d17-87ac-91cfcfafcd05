import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  InvestigationRecord,
  Incident,
} from '../models';
import {InvestigationRecordRepository} from '../repositories';

export class InvestigationRecordIncidentController {
  constructor(
    @repository(InvestigationRecordRepository)
    public investigationRecordRepository: InvestigationRecordRepository,
  ) { }

  @get('/investigation-records/{id}/incident', {
    responses: {
      '200': {
        description: 'Incident belonging to InvestigationRecord',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Incident),
          },
        },
      },
    },
  })
  async getIncident(
    @param.path.string('id') id: typeof InvestigationRecord.prototype.id,
  ): Promise<Incident> {
    return this.investigationRecordRepository.incident(id);
  }
}
