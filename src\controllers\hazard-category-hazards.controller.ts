import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  HazardCategory,
  Hazards,
} from '../models';
import {HazardCategoryRepository} from '../repositories';

export class HazardCategoryHazardsController {
  constructor(
    @repository(HazardCategoryRepository) protected hazardCategoryRepository: HazardCategoryRepository,
  ) { }

  @get('/hazard-categories/{id}/hazards', {
    responses: {
      '200': {
        description: 'Array of HazardCategory has many Hazards',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Hazards)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Hazards>,
  ): Promise<Hazards[]> {
    return this.hazardCategoryRepository.hazards(id).find(filter);
  }

  @post('/hazard-categories/{id}/hazards', {
    responses: {
      '200': {
        description: 'HazardCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(Hazards)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof HazardCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Hazards, {
            title: 'NewHazardsInHazardCategory',
            exclude: ['id'],
            optional: ['hazardCategoryId']
          }),
        },
      },
    }) hazards: Omit<Hazards, 'id'>,
  ): Promise<Hazards> {
    return this.hazardCategoryRepository.hazards(id).create(hazards);
  }

  @patch('/hazard-categories/{id}/hazards', {
    responses: {
      '200': {
        description: 'HazardCategory.Hazards PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Hazards, {partial: true}),
        },
      },
    })
    hazards: Partial<Hazards>,
    @param.query.object('where', getWhereSchemaFor(Hazards)) where?: Where<Hazards>,
  ): Promise<Count> {
    return this.hazardCategoryRepository.hazards(id).patch(hazards, where);
  }

  @del('/hazard-categories/{id}/hazards', {
    responses: {
      '200': {
        description: 'HazardCategory.Hazards DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Hazards)) where?: Where<Hazards>,
  ): Promise<Count> {
    return this.hazardCategoryRepository.hazards(id).delete(where);
  }
}
