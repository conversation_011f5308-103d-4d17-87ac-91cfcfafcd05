import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {InvestigationRecommendation} from '../models';
import {InvestigationRecommendationRepository} from '../repositories';

export class InvestigationRecommendationController {
  constructor(
    @repository(InvestigationRecommendationRepository)
    public investigationRecommendationRepository : InvestigationRecommendationRepository,
  ) {}

  @post('/investigation-recommendations')
  @response(200, {
    description: 'InvestigationRecommendation model instance',
    content: {'application/json': {schema: getModelSchemaRef(InvestigationRecommendation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecommendation, {
            title: 'NewInvestigationRecommendation',
            exclude: ['id'],
          }),
        },
      },
    })
    investigationRecommendation: Omit<InvestigationRecommendation, 'id'>,
  ): Promise<InvestigationRecommendation> {
    return this.investigationRecommendationRepository.create(investigationRecommendation);
  }

  @get('/investigation-recommendations/count')
  @response(200, {
    description: 'InvestigationRecommendation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(InvestigationRecommendation) where?: Where<InvestigationRecommendation>,
  ): Promise<Count> {
    return this.investigationRecommendationRepository.count(where);
  }

  @get('/investigation-recommendations')
  @response(200, {
    description: 'Array of InvestigationRecommendation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(InvestigationRecommendation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(InvestigationRecommendation) filter?: Filter<InvestigationRecommendation>,
  ): Promise<InvestigationRecommendation[]> {
    return this.investigationRecommendationRepository.find(filter);
  }

  @patch('/investigation-recommendations')
  @response(200, {
    description: 'InvestigationRecommendation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecommendation, {partial: true}),
        },
      },
    })
    investigationRecommendation: InvestigationRecommendation,
    @param.where(InvestigationRecommendation) where?: Where<InvestigationRecommendation>,
  ): Promise<Count> {
    return this.investigationRecommendationRepository.updateAll(investigationRecommendation, where);
  }

  @get('/investigation-recommendations/{id}')
  @response(200, {
    description: 'InvestigationRecommendation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(InvestigationRecommendation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(InvestigationRecommendation, {exclude: 'where'}) filter?: FilterExcludingWhere<InvestigationRecommendation>
  ): Promise<InvestigationRecommendation> {
    return this.investigationRecommendationRepository.findById(id, filter);
  }

  @patch('/investigation-recommendations/{id}')
  @response(204, {
    description: 'InvestigationRecommendation PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvestigationRecommendation, {partial: true}),
        },
      },
    })
    investigationRecommendation: InvestigationRecommendation,
  ): Promise<void> {
    await this.investigationRecommendationRepository.updateById(id, investigationRecommendation);
  }

  @put('/investigation-recommendations/{id}')
  @response(204, {
    description: 'InvestigationRecommendation PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() investigationRecommendation: InvestigationRecommendation,
  ): Promise<void> {
    await this.investigationRecommendationRepository.replaceById(id, investigationRecommendation);
  }

  @del('/investigation-recommendations/{id}')
  @response(204, {
    description: 'InvestigationRecommendation DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.investigationRecommendationRepository.deleteById(id);
  }
}
