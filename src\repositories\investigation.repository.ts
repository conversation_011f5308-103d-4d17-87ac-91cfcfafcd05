import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {Investigation, InvestigationRelations, Incident, User, InvestigationRecommendation} from '../models';
import {IncidentRepository} from './incident.repository';
import {UserRepository} from './user.repository';
import {InvestigationRecommendationRepository} from './investigation-recommendation.repository';

export class InvestigationRepository extends DefaultCrudRepository<
  Investigation,
  typeof Investigation.prototype.id,
  InvestigationRelations
> {

  public readonly incident: BelongsToAccessor<Incident, typeof Investigation.prototype.id>;

  public readonly investigator: <PERSON>ong<PERSON>ToAccessor<User, typeof Investigation.prototype.id>;

  public readonly investigationRecommendations: HasManyRepositoryFactory<InvestigationRecommendation, typeof Investigation.prototype.id>;

  public readonly investigationApprover: BelongsToAccessor<User, typeof Investigation.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('IncidentRepository') protected incidentRepositoryGetter: Getter<IncidentRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('InvestigationRecommendationRepository') protected investigationRecommendationRepositoryGetter: Getter<InvestigationRecommendationRepository>,
  ) {
    super(Investigation, dataSource);
    this.investigationApprover = this.createBelongsToAccessorFor('investigationApprover', userRepositoryGetter,);
    this.registerInclusionResolver('investigationApprover', this.investigationApprover.inclusionResolver);
    this.investigationRecommendations = this.createHasManyRepositoryFactoryFor('investigationRecommendations', investigationRecommendationRepositoryGetter,);
    this.registerInclusionResolver('investigationRecommendations', this.investigationRecommendations.inclusionResolver);
    this.investigator = this.createBelongsToAccessorFor('investigator', userRepositoryGetter,);
    this.registerInclusionResolver('investigator', this.investigator.inclusionResolver);
    this.incident = this.createBelongsToAccessorFor('incident', incidentRepositoryGetter,);
    this.registerInclusionResolver('incident', this.incident.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
