import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Incident,
  LocationTwo,
} from '../models';
import {IncidentRepository} from '../repositories';

export class IncidentLocationTwoController {
  constructor(
    @repository(IncidentRepository)
    public incidentRepository: IncidentRepository,
  ) { }

  @get('/incidents/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to Incident',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof Incident.prototype.id,
  ): Promise<LocationTwo> {
    return this.incidentRepository.locationTwo(id);
  }
}
