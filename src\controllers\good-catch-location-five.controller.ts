import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  LocationFive,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchLocationFiveController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/location-five', {
    responses: {
      '200': {
        description: 'LocationFive belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFive),
          },
        },
      },
    },
  })
  async getLocationFive(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<LocationFive> {
    return this.goodCatchRepository.locationFive(id);
  }
}
