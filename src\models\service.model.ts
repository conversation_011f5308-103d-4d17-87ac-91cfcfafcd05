import {Entity, hasMany, model, property} from '@loopback/repository';
import {Action} from './action.model';
import {Dropdown} from './dropdown.model';
import {Role} from './role.model';
import {TenantService} from './tenant-service.model';
import {Tenant} from './tenant.model';

@model()
export class Service extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
    mongodb: {
      unique: true,
    },

  })
  maskName?: string;

  @property({
    type: 'string',
  })
  support?: 'Web' | 'Mobile' | 'Both';

  @property({
    type: 'string',
  })
  mobileShortName?: string;

  @property({
    type: 'string',
  })
  color?: string;

  @property({
    type: 'string',
  })
  icon?: string;

  @property({
    type: 'string',
  })
  applicability?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  status?: boolean;

  @property({
    type: 'string',
  })
  url?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @hasMany(() => Role)
  roles: Role[];

  @hasMany(() => Tenant, {through: {model: () => TenantService}})
  tenants: Tenant[];

  @hasMany(() => Dropdown)
  dropdowns: Dropdown[];

  @hasMany(() => Action)
  actions: Action[];

  constructor(data?: Partial<Service>) {
    super(data);
  }
}

export interface ServiceRelations {
  // describe navigational properties here
}

export type ServiceWithRelations = Service & ServiceRelations;
