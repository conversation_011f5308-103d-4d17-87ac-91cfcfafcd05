import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  LocationThree,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchLocationThreeController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to <PERSON><PERSON><PERSON>',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<LocationThree> {
    return this.goodCatchRepository.locationThree(id);
  }
}
