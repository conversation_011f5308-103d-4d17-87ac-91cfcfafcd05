import {Provider, inject} from '@loopback/core';
import {juggler} from '@loopback/repository';
import {Request, RequestContext, RestBindings} from '@loopback/rest';
import {MongoClient, MongoClientOptions} from 'mongodb';

// Global datasource cache to persist across requests
const datasourceCache: Map<string, {dataSource: juggler.DataSource, client: MongoClient}> = new Map();
// To track the creation promises for DataSources, to avoid concurrent creations
const datasourceCreationLocks: Map<string, Promise<juggler.DataSource>> = new Map();

export class DynamicDataSourceProvider implements Provider<juggler.DataSource> {
  private mongoConnectionPromise: Promise<MongoClient> | null = null;

  constructor(
    @inject(RestBindings.Http.CONTEXT, {optional: true}) private reqCtx: RequestContext, // Inject the RequestContext
  ) { }

  // Ensures MongoClient connects only once and reuses the connection
  private async connectToMongo(): Promise<MongoClient> {
    const uri = `mongodb+srv://${process.env.DB_URL}/?retryWrites=true&w=majority`;
    const options: MongoClientOptions = {
      maxPoolSize: 50,  // Reduce pool size
      minPoolSize: 0,   // Maintain at least 2 idle connections
      maxIdleTimeMS: 30000, // Close idle connections after 30 seconds
      socketTimeoutMS: 60000, // Timeout for socket operations
      serverSelectionTimeoutMS: 30000, // Timeout for server selection
      readPreference: 'primary', // Limit connections to the primary node
    };

    if (!this.mongoConnectionPromise) {
      console.log("Establishing MongoDB connection...");
      this.mongoConnectionPromise = new MongoClient(uri, options).connect();
    } else {
      console.log("Reusing existing MongoDB connection.");
    }

    return this.mongoConnectionPromise;
  }

  // Ensures MongoClient disconnects from the server


  // Provider value that returns the DataSource
  async value(): Promise<juggler.DataSource> {
    console.log("Providing DataSource...");
    return this.getDataSource();
  }

  // Core logic to retrieve or create the DataSource for the request
  async getDataSource(): Promise<juggler.DataSource> {
    console.log("Retrieving DataSource for the request...");

    const request = this.reqCtx.request;
    const enterpriseId = request.headers['x-enterprise-id'];
    let database: string;

    if (enterpriseId) {
      database = this.getDatabaseNameFromEnterpriseId(enterpriseId as string);
    } else {
      const subdomain = this.extractSubdomain(request);
      database = this.getDatabaseNameFromSubdomain(subdomain);
    }

    console.log(`Resolved database: ${database}`);

    // Check if DataSource for the database is already cached and valid
    if (datasourceCache.has(database)) {
      console.log(`Reusing cached DataSource for ${database}`);
      return datasourceCache.get(database)!.dataSource;
    }

    // If a DataSource is being created for the database, wait for it
    if (datasourceCreationLocks.has(database)) {
      console.log(`Waiting for DataSource creation to complete for ${database}`);
      return datasourceCreationLocks.get(database)!;
    }

    // Lock the DataSource creation to prevent other requests from creating it
    const creationPromise = this.createAndCacheDataSource(database);
    datasourceCreationLocks.set(database, creationPromise);

    // Once creation is complete, remove the lock
    creationPromise.finally(() => {
      datasourceCreationLocks.delete(database);
    }).catch(error => {
      console.error(`Error in DataSource creation for ${database}:`, error);
    });

    return creationPromise;
  }

  // Helper to create and cache the DataSource
  private async createAndCacheDataSource(database: string): Promise<juggler.DataSource> {
    console.log(`Creating new DataSource for ${database}`);
    const mongoClient = await this.connectToMongo();

    // Create new DataSource configuration using the shared MongoClient
    const config = {
      name: 'MongoDb',
      connector: 'mongodb',
      url: `mongodb+srv://${process.env.DB_URL}/${database}?retryWrites=true&w=majority`,
      client: mongoClient, // Use the newly created MongoClient instance
    };

    try {
      const dataSource = new juggler.DataSource(config);
      datasourceCache.set(database, {dataSource, client: mongoClient});
      console.log(`DataSource for ${database} cached successfully.`);
      return dataSource;
    } catch (error) {
      console.error(`Failed to create DataSource for ${database}:`, error);
      throw new Error(`Unable to connect to the database: ${database}`);
    }
  }

  // Helper to get the database name based on subdomain
  getDatabaseNameFromSubdomain(subdomain: string | undefined): string {
    const sanitizedSubdomain = subdomain ? subdomain.replace(/-admin$/, '') : undefined;

    // Return the database name with 'Db' suffix or 'defaultDb' if subdomain is undefined
    return sanitizedSubdomain ? `${sanitizedSubdomain}Db` : 'defaultDb';
  }

  // Helper to get the database name based on enterprise ID
  getDatabaseNameFromEnterpriseId(enterpriseId: string): string {
    return enterpriseId ? `${enterpriseId}Db` : 'defaultDb';
  }

  // Extract subdomain from the request object
  extractSubdomain(request: Request): string | undefined {
    const referer = request.headers.referer ?? request.headers.origin;
    if (!referer) {
      console.log('No referer or origin header found');
      return undefined;
    }

    try {
      const url = new URL(referer);
      const host = url.hostname;
      const parts = host.split('.');

      // If localhost or 127.0.0.1, return "internal"
      if (host === 'localhost' || host === '127.0.0.1') {
        return 'internal';
      }

      if (parts.length < 2) {
        return undefined; // No subdomain present
      }

      let subdomain = parts[0];

      // If the subdomain starts with "app.", extract the second item instead
      if (subdomain === 'app' && parts.length > 2) {
        subdomain = parts[1];
      }

      // Remove "-admin" suffix if present
      if (subdomain.endsWith('-admin')) {
        subdomain = subdomain.replace('-admin', '');
      }

      console.log(`Extracted subdomain: ${subdomain}`);
      return subdomain;
    } catch (error) {
      console.error('Error parsing referer/origin URL:', error);
      return undefined;
    }
  }

}
