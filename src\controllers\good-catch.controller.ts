import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GoodCatch} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository : GoodCatchRepository,
  ) {}

  @post('/good-catches')
  @response(200, {
    description: 'GoodCatch model instance',
    content: {'application/json': {schema: getModelSchemaRef(GoodCatch)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, {
            title: 'NewGoodCatch',
            exclude: ['id'],
          }),
        },
      },
    })
    goodCatch: Omit<GoodCatch, 'id'>,
  ): Promise<GoodCatch> {
    return this.goodCatchRepository.create(goodCatch);
  }

  @get('/good-catches/count')
  @response(200, {
    description: 'GoodCatch model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GoodCatch) where?: Where<GoodCatch>,
  ): Promise<Count> {
    return this.goodCatchRepository.count(where);
  }

  @get('/good-catches')
  @response(200, {
    description: 'Array of GoodCatch model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GoodCatch, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GoodCatch) filter?: Filter<GoodCatch>,
  ): Promise<GoodCatch[]> {
    return this.goodCatchRepository.find(filter);
  }

  @patch('/good-catches')
  @response(200, {
    description: 'GoodCatch PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, {partial: true}),
        },
      },
    })
    goodCatch: GoodCatch,
    @param.where(GoodCatch) where?: Where<GoodCatch>,
  ): Promise<Count> {
    return this.goodCatchRepository.updateAll(goodCatch, where);
  }

  @get('/good-catches/{id}')
  @response(200, {
    description: 'GoodCatch model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GoodCatch, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(GoodCatch, {exclude: 'where'}) filter?: FilterExcludingWhere<GoodCatch>
  ): Promise<GoodCatch> {
    return this.goodCatchRepository.findById(id, filter);
  }

  @patch('/good-catches/{id}')
  @response(204, {
    description: 'GoodCatch PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GoodCatch, {partial: true}),
        },
      },
    })
    goodCatch: GoodCatch,
  ): Promise<void> {
    await this.goodCatchRepository.updateById(id, goodCatch);
  }

  @put('/good-catches/{id}')
  @response(204, {
    description: 'GoodCatch PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() goodCatch: GoodCatch,
  ): Promise<void> {
    await this.goodCatchRepository.replaceById(id, goodCatch);
  }

  @del('/good-catches/{id}')
  @response(204, {
    description: 'GoodCatch DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.goodCatchRepository.deleteById(id);
  }
}
