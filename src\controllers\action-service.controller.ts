import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Action,
  Service,
} from '../models';
import {ActionRepository} from '../repositories';

export class ActionServiceController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
  ) { }

  @get('/actions/{id}/service', {
    responses: {
      '200': {
        description: 'Service belonging to Action',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Service),
          },
        },
      },
    },
  })
  async getService(
    @param.path.string('id') id: typeof Action.prototype.id,
  ): Promise<Service> {
    return this.actionRepository.service(id);
  }
}
