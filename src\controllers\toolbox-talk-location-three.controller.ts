import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  LocationThree,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkLocationThreeController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<LocationThree> {
    return this.toolboxTalkRepository.locationThree(id);
  }
}
