import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Action,
  User,
} from '../models';
import {ActionRepository} from '../repositories';

export class ActionUserController {
  constructor(
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
  ) { }

  @get('/actions/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to Action',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof Action.prototype.id,
  ): Promise<User> {
    return this.actionRepository.submittedBy(id);
  }
}
