import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  OttTask,
  Ott,
} from '../models';
import {OttTaskRepository} from '../repositories';

export class OttTaskOttController {
  constructor(
    @repository(OttTaskRepository)
    public ottTaskRepository: OttTaskRepository,
  ) { }

  @get('/ott-tasks/{id}/ott', {
    responses: {
      '200': {
        description: 'Ott belonging to OttTask',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Ott),
          },
        },
      },
    },
  })
  async getOtt(
    @param.path.string('id') id: typeof OttTask.prototype.id,
  ): Promise<Ott> {
    return this.ottTaskRepository.ott(id);
  }
}
