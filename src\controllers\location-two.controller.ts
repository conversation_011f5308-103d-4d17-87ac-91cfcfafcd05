import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {LocationTwo} from '../models';
import {LocationTwoRepository} from '../repositories';

export class LocationTwoController {
  constructor(
    @repository(LocationTwoRepository)
    public locationTwoRepository : LocationTwoRepository,
  ) {}

  @post('/location-twos')
  @response(200, {
    description: 'LocationTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(LocationTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationTwo, {
            title: 'NewLocationTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    locationTwo: Omit<LocationTwo, 'id'>,
  ): Promise<LocationTwo> {
    return this.locationTwoRepository.create(locationTwo);
  }

  @get('/location-twos/count')
  @response(200, {
    description: 'LocationTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(LocationTwo) where?: Where<LocationTwo>,
  ): Promise<Count> {
    return this.locationTwoRepository.count(where);
  }

  @get('/location-twos')
  @response(200, {
    description: 'Array of LocationTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(LocationTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(LocationTwo) filter?: Filter<LocationTwo>,
  ): Promise<LocationTwo[]> {
    return this.locationTwoRepository.find(filter);
  }

  @patch('/location-twos')
  @response(200, {
    description: 'LocationTwo PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationTwo, {partial: true}),
        },
      },
    })
    locationTwo: LocationTwo,
    @param.where(LocationTwo) where?: Where<LocationTwo>,
  ): Promise<Count> {
    return this.locationTwoRepository.updateAll(locationTwo, where);
  }

  @get('/location-twos/{id}')
  @response(200, {
    description: 'LocationTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(LocationTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<LocationTwo>
  ): Promise<LocationTwo> {
    return this.locationTwoRepository.findById(id, filter);
  }

  @patch('/location-twos/{id}')
  @response(204, {
    description: 'LocationTwo PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationTwo, {partial: true}),
        },
      },
    })
    locationTwo: LocationTwo,
  ): Promise<void> {
    await this.locationTwoRepository.updateById(id, locationTwo);
  }

  @put('/location-twos/{id}')
  @response(204, {
    description: 'LocationTwo PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() locationTwo: LocationTwo,
  ): Promise<void> {
    await this.locationTwoRepository.replaceById(id, locationTwo);
  }

  @del('/location-twos/{id}')
  @response(204, {
    description: 'LocationTwo DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.locationTwoRepository.deleteById(id);
  }
}
