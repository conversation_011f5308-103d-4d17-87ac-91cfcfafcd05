import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {RiskUpdate} from '../models';
import {RiskUpdateRepository} from '../repositories';

export class RiskUpdateController {
  constructor(
    @repository(RiskUpdateRepository)
    public riskUpdateRepository : RiskUpdateRepository,
  ) {}

  @post('/risk-updates')
  @response(200, {
    description: 'RiskUpdate model instance',
    content: {'application/json': {schema: getModelSchemaRef(RiskUpdate)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, {
            title: 'NewRiskUpdate',
            exclude: ['id'],
          }),
        },
      },
    })
    riskUpdate: Omit<RiskUpdate, 'id'>,
  ): Promise<RiskUpdate> {
    return this.riskUpdateRepository.create(riskUpdate);
  }

  @get('/risk-updates/count')
  @response(200, {
    description: 'RiskUpdate model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(RiskUpdate) where?: Where<RiskUpdate>,
  ): Promise<Count> {
    return this.riskUpdateRepository.count(where);
  }

  @get('/risk-updates')
  @response(200, {
    description: 'Array of RiskUpdate model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(RiskUpdate, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(RiskUpdate) filter?: Filter<RiskUpdate>,
  ): Promise<RiskUpdate[]> {
    return this.riskUpdateRepository.find(filter);
  }

  @patch('/risk-updates')
  @response(200, {
    description: 'RiskUpdate PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, {partial: true}),
        },
      },
    })
    riskUpdate: RiskUpdate,
    @param.where(RiskUpdate) where?: Where<RiskUpdate>,
  ): Promise<Count> {
    return this.riskUpdateRepository.updateAll(riskUpdate, where);
  }

  @get('/risk-updates/{id}')
  @response(200, {
    description: 'RiskUpdate model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(RiskUpdate, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(RiskUpdate, {exclude: 'where'}) filter?: FilterExcludingWhere<RiskUpdate>
  ): Promise<RiskUpdate> {
    return this.riskUpdateRepository.findById(id, filter);
  }

  @patch('/risk-updates/{id}')
  @response(204, {
    description: 'RiskUpdate PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(RiskUpdate, {partial: true}),
        },
      },
    })
    riskUpdate: RiskUpdate,
  ): Promise<void> {
    await this.riskUpdateRepository.updateById(id, riskUpdate);
  }

  @put('/risk-updates/{id}')
  @response(204, {
    description: 'RiskUpdate PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() riskUpdate: RiskUpdate,
  ): Promise<void> {
    await this.riskUpdateRepository.replaceById(id, riskUpdate);
  }

  @del('/risk-updates/{id}')
  @response(204, {
    description: 'RiskUpdate DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.riskUpdateRepository.deleteById(id);
  }
}
