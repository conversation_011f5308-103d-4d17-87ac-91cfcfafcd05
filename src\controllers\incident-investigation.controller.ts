import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Incident,
  Investigation,
} from '../models';
import {IncidentRepository, InvestigationRepository} from '../repositories';

export class IncidentInvestigationController {
  constructor(
    @repository(IncidentRepository) protected incidentRepository: IncidentRepository,
    @repository(InvestigationRepository) protected investigationRepository: InvestigationRepository,
  ) { }

  @get('/incidents/{id}/investigation', {
    responses: {
      '200': {
        description: 'Incident has one Investigation',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Investigation),
          },
        },
      },
    },
  })
  async get(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Investigation>,
  ): Promise<Investigation> {
    return this.incidentRepository.investigation(id).get(filter);
  }

  @post('/incidents/{id}/investigation', {
    responses: {
      '200': {
        description: 'Incident model instance',
        content: {'application/json': {schema: getModelSchemaRef(Investigation)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Incident.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {
            title: 'NewInvestigationInIncident',
            exclude: ['id'],
            optional: ['incidentId']
          }),
        },
      },
    }) investigation: Omit<Investigation, 'id'>,
  ): Promise<Investigation> {
    return this.incidentRepository.investigation(id).create(investigation);
  }

  @patch('/incidents/{id}/investigation', {
    responses: {
      '200': {
        description: 'Incident.Investigation PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Investigation, {partial: true}),
        },
      },
    })
    investigation: Partial<Investigation>,
    @param.query.object('where', getWhereSchemaFor(Investigation)) where?: Where<Investigation>,
  ): Promise<Count> {
    return this.incidentRepository.investigation(id).patch(investigation, where);
  }

  @del('/incidents/{id}/investigation', {
    responses: {
      '200': {
        description: 'Incident.Investigation DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Investigation)) where?: Where<Investigation>,
  ): Promise<Count> {
    return this.incidentRepository.investigation(id).delete(where);
  }
}
