import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RiskAssessment,
  Department,
} from '../models';
import {RiskAssessmentRepository} from '../repositories';

export class RiskAssessmentDepartmentController {
  constructor(
    @repository(RiskAssessmentRepository)
    public riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @get('/risk-assessments/{id}/department', {
    responses: {
      '200': {
        description: 'Department belonging to RiskAssessment',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Department),
          },
        },
      },
    },
  })
  async getDepartment(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
  ): Promise<Department> {
    return this.riskAssessmentRepository.department(id);
  }
}
