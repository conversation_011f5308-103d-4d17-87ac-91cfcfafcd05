import {Entity, model, property} from '@loopback/repository';

@model()
export class ImmediateCause extends Entity {

  @property({
    type: 'string',
    required: false,
    description: 'What were the first actions taken to control or contain the situation?',
  })
  initialActions?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Was first aid or medical attention provided to any personnel involved?',
  })
  medicalAttentionProvided?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were any emergency services (firefighters, paramedics, police) called?',
  })
  emergencyServicesCalled?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Was the affected area secured or isolated to prevent further incidents?',
  })
  areaSecured?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were any safety controls (e.g., shutdown of equipment, emergency stop) activated?',
  })
  safetyControlsActivated?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were evacuation procedures initiated, and if so, how were they managed?',
  })
  evacuationInitiated?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Were any hazardous materials contained, cleaned up, or neutralized?',
  })
  hazardousMaterialsHandled?: string;

  @property({
    type: 'string',
    required: false,
    description: 'Was there any communication with management, safety officers, or external authorities?',
  })
  communicationWithAuthorities?: string;

  @property({
    type: 'string',
    required: false
  })
  status?: string;

  constructor(data?: Partial<ImmediateCause>) {
    super(data);
  }
}

export interface ImmediateCauseRelations {
  // describe navigational properties here
}

export type ImmediateCauseWithRelations = ImmediateCause & ImmediateCauseRelations;
