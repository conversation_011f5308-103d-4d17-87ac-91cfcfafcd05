import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  RaTeamMember,
  User,
} from '../models';
import {RaTeamMemberRepository} from '../repositories';

export class RaTeamMemberUserController {
  constructor(
    @repository(RaTeamMemberRepository)
    public raTeamMemberRepository: RaTeamMemberRepository,
  ) { }

  @get('/ra-team-members/{id}/user', {
    responses: {
      '200': {
        description: 'User belonging to RaTeamMember',
        content: {
          'application/json': {
            schema: getModelSchemaRef(User),
          },
        },
      },
    },
  })
  async getUser(
    @param.path.string('id') id: typeof RaTeamMember.prototype.id,
  ): Promise<User> {
    return this.raTeamMemberRepository.user(id);
  }
}
