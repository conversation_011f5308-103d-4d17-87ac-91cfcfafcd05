import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  LocationTwo,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkLocationTwoController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<LocationTwo> {
    return this.toolboxTalkRepository.locationTwo(id);
  }
}
