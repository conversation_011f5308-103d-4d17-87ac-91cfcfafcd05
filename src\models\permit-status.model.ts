import {Entity, model, property} from '@loopback/repository';

@model()
export class PermitStatus extends Entity {

  @property({
    type: 'string',
  })
  signature?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  by?: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  uploads?: string[];

  @property({
    type: 'string',
  })
  byEmail?: string;

  @property({
    type: 'date',
  })
  signedDate?: string;

  @property({
    type: 'string',
  })
  comments?: string;



  constructor(data?: Partial<PermitStatus>) {
    super(data);
  }
}

export interface PermitStatusRelations {
  // describe navigational properties here
}

export type PermitStatusWithRelations = PermitStatus & PermitStatusRelations;
