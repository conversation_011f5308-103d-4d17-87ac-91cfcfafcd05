import {Entity, model, property} from '@loopback/repository';

@model()
export class ConsequenceDescription extends Entity {

  @property({
    type: 'string',
  })
  question?: string;

  @property({
    type: 'string',
  })
  description?: string;

  constructor(data?: Partial<ConsequenceDescription>) {
    super(data);
  }
}

export interface ConsequenceDescriptionRelations {
  // describe navigational properties here
}

export type ConsequenceDescriptionWithRelations = ConsequenceDescription & ConsequenceDescriptionRelations;
