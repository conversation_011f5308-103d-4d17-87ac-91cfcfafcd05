import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, juggler, repository, BelongsToAccessor} from '@loopback/repository';
import {RiskUpdate, RiskUpdateRelations, RiskAssessment} from '../models';
import {RiskAssessmentRepository} from './risk-assessment.repository';

export class RiskUpdateRepository extends DefaultCrudRepository<
  RiskUpdate,
  typeof RiskUpdate.prototype.id,
  RiskUpdateRelations
> {

  public readonly riskAssessment: BelongsToAccessor<RiskAssessment, typeof RiskUpdate.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('RiskAssessmentRepository') protected riskAssessmentRepositoryGetter: Getter<RiskAssessmentRepository>,
  ) {
    super(RiskUpdate, dataSource);
    this.riskAssessment = this.createBelongsToAccessorFor('riskAssessment', riskAssessmentRepositoryGetter,);
    this.registerInclusionResolver('riskAssessment', this.riskAssessment.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
