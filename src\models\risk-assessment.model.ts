import {belongsTo, Entity, hasMany, hasOne, model, property} from '@loopback/repository';
import {Department} from './department.model';
import {RaTeamMember} from './ra-team-member.model';
import {RiskUpdate} from './risk-update.model';
import {ToolboxTalk} from './toolbox-talk.model';
import {User} from './user.model';
import {WorkActivity} from './work-activity.model';

@model()
export class RiskAssessment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  shortName?: string;

  @property({
    type: 'string',
  })
  hazardName?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'any',


  })
  tasks?: any;

  @property({
    type: 'object',
  })
  overallRecommendationOne?: object;

  @property({
    type: 'object',
  })
  overallRecommendationTwo?: object;

  @property({
    type: 'array',
    itemType: 'object',
  })
  highRisk?: object[];

  @property({
    type: 'string',
  })
  shortId?: string;

  @property({
    type: 'string',
  })
  additonalRemarks?: string;

  @property({
    type: 'object',
  })
  teamLeaderDeclaration?: object;

  @property({
    type: 'string',
  })
  nonRoutineDepartment?: string;

  @property({
    type: 'string',
  })
  nonRoutineWorkActivity?: string;

  @property({
    type: 'string',
  })
  status?: 'Pending' | 'Draft' | 'Published' | 'Deleted';

  @property({
    type: 'date',
  })
  nextReviewDate?: string;

  @property({
    type: 'date',
  })
  publishedDate?: string;

  @property({
    type: 'string',
  })
  type?: 'Routine' | 'Non Routine' | 'High-Risk Hazard';

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'number',
  })
  level?: number;

  @hasMany(() => RiskUpdate)
  riskUpdates: RiskUpdate[];

  @belongsTo(() => User)
  teamLeaderId: string;

  @hasMany(() => RaTeamMember)
  raTeamMembers: RaTeamMember[];

  @belongsTo(() => Department)
  departmentId: string;

  @belongsTo(() => WorkActivity)
  workActivityId: string;

  workActivity?: WorkActivity;

  @hasOne(() => ToolboxTalk)
  toolboxTalk: ToolboxTalk;

  constructor(data?: Partial<RiskAssessment>) {
    super(data);
  }
}

export interface RiskAssessmentRelations {
  // describe navigational properties here
}

export type RiskAssessmentWithRelations = RiskAssessment & RiskAssessmentRelations;
