import {S3Client} from "@aws-sdk/client-s3";
import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ConfigRepository} from '../repositories';

@injectable({scope: BindingScope.SINGLETON})
export class ConfigService {
  private s3Client: S3Client | undefined;

  constructor(
    @repository(ConfigRepository)
    private configRepository: ConfigRepository,
  ) { }

  async getConfigValue(key: string): Promise<string> {
    const config = await this.configRepository.findOne({where: {key}});
    if (!config?.value) {
      throw new Error(`Config value for key ${key} not found`);
    }
    return config.value;
  }

  async getS3Client(): Promise<S3Client> {
    if (!this.s3Client) {
      const region = await this.getConfigValue('COGNITO_REGION');
      const accessKeyId = await this.getConfigValue('AWS_ACCESS_KEY_ID');
      const secretAccessKey = await this.getConfigValue('AWS_SECRET_ACCESS_KEY');

      this.s3Client = new S3Client({
        region,
        credentials: {
          accessKeyId,
          secretAccessKey,
        },
      });
    }
    return this.s3Client;
  }

  async getBucketName(): Promise<string> {
    return this.getConfigValue('AWS_S3_BUCKET_NAME');
  }
}
