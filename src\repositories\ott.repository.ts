import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {Ott, OttRelations, User, OttTask, DropdownItems} from '../models';
import {UserRepository} from './user.repository';
import {OttTaskRepository} from './ott-task.repository';
import {DropdownItemsRepository} from './dropdown-items.repository';

export class OttRepository extends DefaultCrudRepository<
  Ott,
  typeof Ott.prototype.id,
  OttRelations
> {

  public readonly creator: BelongsToAccessor<User, typeof Ott.prototype.id>;

  public readonly assignee: BelongsToAccessor<User, typeof Ott.prototype.id>;

  public readonly reviewer: BelongsToAccessor<User, typeof Ott.prototype.id>;

  public readonly ottTasks: HasManyRepositoryFactory<OttTask, typeof Ott.prototype.id>;

  public readonly project: BelongsToAccessor<DropdownItems, typeof Ott.prototype.id>;

  public readonly category: BelongsToAccessor<DropdownItems, typeof Ott.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>, @repository.getter('OttTaskRepository') protected ottTaskRepositoryGetter: Getter<OttTaskRepository>, @repository.getter('DropdownItemsRepository') protected dropdownItemsRepositoryGetter: Getter<DropdownItemsRepository>,
  ) {
    super(Ott, dataSource);
    this.category = this.createBelongsToAccessorFor('category', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('category', this.category.inclusionResolver);
    this.project = this.createBelongsToAccessorFor('project', dropdownItemsRepositoryGetter,);
    this.registerInclusionResolver('project', this.project.inclusionResolver);
    this.ottTasks = this.createHasManyRepositoryFactoryFor('ottTasks', ottTaskRepositoryGetter,);
    this.registerInclusionResolver('ottTasks', this.ottTasks.inclusionResolver);
    this.reviewer = this.createBelongsToAccessorFor('reviewer', userRepositoryGetter,);
    this.registerInclusionResolver('reviewer', this.reviewer.inclusionResolver);
    this.assignee = this.createBelongsToAccessorFor('assignee', userRepositoryGetter,);
    this.registerInclusionResolver('assignee', this.assignee.inclusionResolver);
    this.creator = this.createBelongsToAccessorFor('creator', userRepositoryGetter,);
    this.registerInclusionResolver('creator', this.creator.inclusionResolver);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
