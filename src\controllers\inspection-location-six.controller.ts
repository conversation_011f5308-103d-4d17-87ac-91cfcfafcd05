import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Inspection,
  LocationSix,
} from '../models';
import {InspectionRepository} from '../repositories';

export class InspectionLocationSixController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
  ) { }

  @get('/inspections/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to Inspection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationSix),
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof Inspection.prototype.id,
  ): Promise<LocationSix> {
    return this.inspectionRepository.locationSix(id);
  }
}
