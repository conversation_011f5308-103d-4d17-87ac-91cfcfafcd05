import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ObservationReport,
  LocationSix,
} from '../models';
import {ObservationReportRepository} from '../repositories';

export class ObservationReportLocationSixController {
  constructor(
    @repository(ObservationReportRepository)
    public observationReportRepository: ObservationReportRepository,
  ) { }

  @get('/observation-reports/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to ObservationReport',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationSix),
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof ObservationReport.prototype.id,
  ): Promise<LocationSix> {
    return this.observationReportRepository.locationSix(id);
  }
}
