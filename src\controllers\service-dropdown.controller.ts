import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Service,
  Dropdown,
} from '../models';
import {ServiceRepository} from '../repositories';

export class ServiceDropdownController {
  constructor(
    @repository(ServiceRepository) protected serviceRepository: ServiceRepository,
  ) { }

  @get('/services/{id}/dropdowns', {
    responses: {
      '200': {
        description: 'Array of Service has many Dropdown',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Dropdown)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<Dropdown>,
  ): Promise<Dropdown[]> {
    return this.serviceRepository.dropdowns(id).find(filter);
  }

  @post('/services/{id}/dropdowns', {
    responses: {
      '200': {
        description: 'Service model instance',
        content: {'application/json': {schema: getModelSchemaRef(Dropdown)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Service.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Dropdown, {
            title: 'NewDropdownInService',
            exclude: ['id'],
            optional: ['serviceId']
          }),
        },
      },
    }) dropdown: Omit<Dropdown, 'id'>,
  ): Promise<Dropdown> {
    return this.serviceRepository.dropdowns(id).create(dropdown);
  }

  @patch('/services/{id}/dropdowns', {
    responses: {
      '200': {
        description: 'Service.Dropdown PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Dropdown, {partial: true}),
        },
      },
    })
    dropdown: Partial<Dropdown>,
    @param.query.object('where', getWhereSchemaFor(Dropdown)) where?: Where<Dropdown>,
  ): Promise<Count> {
    return this.serviceRepository.dropdowns(id).patch(dropdown, where);
  }

  @del('/services/{id}/dropdowns', {
    responses: {
      '200': {
        description: 'Service.Dropdown DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(Dropdown)) where?: Where<Dropdown>,
  ): Promise<Count> {
    return this.serviceRepository.dropdowns(id).delete(where);
  }
}
