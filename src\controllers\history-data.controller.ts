import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {HistoryData} from '../models';
import {HistoryDataRepository} from '../repositories';

export class HistoryDataController {
  constructor(
    @repository(HistoryDataRepository)
    public historyDataRepository : HistoryDataRepository,
  ) {}

  @post('/history-data')
  @response(200, {
    description: 'HistoryData model instance',
    content: {'application/json': {schema: getModelSchemaRef(HistoryData)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HistoryData, {
            title: 'NewHistoryData',
            exclude: ['id'],
          }),
        },
      },
    })
    historyData: Omit<HistoryData, 'id'>,
  ): Promise<HistoryData> {
    return this.historyDataRepository.create(historyData);
  }

  @get('/history-data/count')
  @response(200, {
    description: 'HistoryData model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(HistoryData) where?: Where<HistoryData>,
  ): Promise<Count> {
    return this.historyDataRepository.count(where);
  }

  @get('/history-data')
  @response(200, {
    description: 'Array of HistoryData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(HistoryData, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(HistoryData) filter?: Filter<HistoryData>,
  ): Promise<HistoryData[]> {
    return this.historyDataRepository.find(filter);
  }

  @patch('/history-data')
  @response(200, {
    description: 'HistoryData PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HistoryData, {partial: true}),
        },
      },
    })
    historyData: HistoryData,
    @param.where(HistoryData) where?: Where<HistoryData>,
  ): Promise<Count> {
    return this.historyDataRepository.updateAll(historyData, where);
  }

  @get('/history-data/{id}')
  @response(200, {
    description: 'HistoryData model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(HistoryData, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(HistoryData, {exclude: 'where'}) filter?: FilterExcludingWhere<HistoryData>
  ): Promise<HistoryData> {
    return this.historyDataRepository.findById(id, filter);
  }

  @patch('/history-data/{id}')
  @response(204, {
    description: 'HistoryData PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(HistoryData, {partial: true}),
        },
      },
    })
    historyData: HistoryData,
  ): Promise<void> {
    await this.historyDataRepository.updateById(id, historyData);
  }

  @put('/history-data/{id}')
  @response(204, {
    description: 'HistoryData PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() historyData: HistoryData,
  ): Promise<void> {
    await this.historyDataRepository.replaceById(id, historyData);
  }

  @del('/history-data/{id}')
  @response(204, {
    description: 'HistoryData DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.historyDataRepository.deleteById(id);
  }
}
