import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  GoodCatch,
  LocationSix,
} from '../models';
import {GoodCatchRepository} from '../repositories';

export class GoodCatchLocationSixController {
  constructor(
    @repository(GoodCatchRepository)
    public goodCatchRepository: GoodCatchRepository,
  ) { }

  @get('/good-catches/{id}/location-six', {
    responses: {
      '200': {
        description: 'LocationSix belonging to GoodCatch',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationSix),
          },
        },
      },
    },
  })
  async getLocationSix(
    @param.path.string('id') id: typeof GoodCatch.prototype.id,
  ): Promise<LocationSix> {
    return this.goodCatchRepository.locationSix(id);
  }
}
