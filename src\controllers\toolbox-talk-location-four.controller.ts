import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ToolboxTalk,
  LocationFour,
} from '../models';
import {ToolboxTalkRepository} from '../repositories';

export class ToolboxTalkLocationFourController {
  constructor(
    @repository(ToolboxTalkRepository)
    public toolboxTalkRepository: ToolboxTalkRepository,
  ) { }

  @get('/toolbox-talks/{id}/location-four', {
    responses: {
      '200': {
        description: 'LocationFour belonging to ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationFour),
          },
        },
      },
    },
  })
  async getLocationFour(
    @param.path.string('id') id: typeof ToolboxTalk.prototype.id,
  ): Promise<LocationFour> {
    return this.toolboxTalkRepository.locationFour(id);
  }
}
