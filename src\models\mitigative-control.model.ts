import {Entity, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {IncidentControl} from './incident-control.model';

@model()
export class MitigativeControl extends Entity {

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(IncidentControl)

  })
  identifiedMitigativeControls?: IncidentControl[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(IncidentControl)

  })
  unIdentifiedMitigativeControls?: IncidentControl[];

  @property({
    type: 'boolean',
  })
  controlIdentified?: boolean;

  @property({
    type: 'boolean',
  })
  controlNotIdentified?: boolean;

  @property({
    type: 'boolean',
  })
  wasHazardControllable?: boolean;

  @property({
    type: 'string',
  })
  explainHazardControllable?: string;

  @property({
    type: 'string',
  })
  status?: "Draft" | "Completed";

  constructor(data?: Partial<MitigativeControl>) {
    super(data);
  }
}

export interface MitigativeControlRelations {
  // describe navigational properties here
}

export type MitigativeControlWithRelations = MitigativeControl & MitigativeControlRelations;
