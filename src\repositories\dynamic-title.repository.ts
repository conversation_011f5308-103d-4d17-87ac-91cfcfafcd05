import {inject} from '@loopback/core';
import {DefaultCrudRepository, juggler} from '@loopback/repository';
import {DynamicTitle, DynamicTitleRelations} from '../models';

export class DynamicTitleRepository extends DefaultCrudRepository<
  DynamicTitle,
  typeof DynamicTitle.prototype.id,
  DynamicTitleRelations
> {
  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource,
  ) {
    super(DynamicTitle, dataSource);

    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
