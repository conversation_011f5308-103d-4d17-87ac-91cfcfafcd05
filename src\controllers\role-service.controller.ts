import {
  repository,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
} from '@loopback/rest';
import {
  Role,
  Service,
} from '../models';
import {RoleRepository} from '../repositories';

// import {authenticate} from '@loopback/authentication';

// @authenticate('cognito-jwt')
export class RoleServiceController {
  constructor(
    @repository(RoleRepository)
    public roleRepository: RoleRepository,
  ) { }

  @get('/roles/{id}/service', {
    responses: {
      '200': {
        description: 'Service belonging to Role',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Service),
          },
        },
      },
    },
  })
  async getService(
    @param.path.string('id') id: typeof Role.prototype.id,
  ): Promise<Service> {
    return this.roleRepository.service(id);
  }
}
