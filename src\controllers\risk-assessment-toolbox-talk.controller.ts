import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  RiskAssessment,
  ToolboxTalk,
} from '../models';
import {RiskAssessmentRepository} from '../repositories';

export class RiskAssessmentToolboxTalkController {
  constructor(
    @repository(RiskAssessmentRepository) protected riskAssessmentRepository: RiskAssessmentRepository,
  ) { }

  @get('/risk-assessments/{id}/toolbox-talk', {
    responses: {
      '200': {
        description: 'RiskAssessment has one ToolboxTalk',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ToolboxTalk),
          },
        },
      },
    },
  })
  async get(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ToolboxTalk>,
  ): Promise<ToolboxTalk> {
    return this.riskAssessmentRepository.toolboxTalk(id).get(filter);
  }

  @post('/risk-assessments/{id}/toolbox-talk', {
    responses: {
      '200': {
        description: 'RiskAssessment model instance',
        content: {'application/json': {schema: getModelSchemaRef(ToolboxTalk)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof RiskAssessment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxTalk, {
            title: 'NewToolboxTalkInRiskAssessment',
            exclude: ['id'],
            optional: ['riskAssessmentId']
          }),
        },
      },
    }) toolboxTalk: Omit<ToolboxTalk, 'id'>,
  ): Promise<ToolboxTalk> {
    return this.riskAssessmentRepository.toolboxTalk(id).create(toolboxTalk);
  }

  @patch('/risk-assessments/{id}/toolbox-talk', {
    responses: {
      '200': {
        description: 'RiskAssessment.ToolboxTalk PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ToolboxTalk, {partial: true}),
        },
      },
    })
    toolboxTalk: Partial<ToolboxTalk>,
    @param.query.object('where', getWhereSchemaFor(ToolboxTalk)) where?: Where<ToolboxTalk>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.toolboxTalk(id).patch(toolboxTalk, where);
  }

  @del('/risk-assessments/{id}/toolbox-talk', {
    responses: {
      '200': {
        description: 'RiskAssessment.ToolboxTalk DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ToolboxTalk)) where?: Where<ToolboxTalk>,
  ): Promise<Count> {
    return this.riskAssessmentRepository.toolboxTalk(id).delete(where);
  }
}
