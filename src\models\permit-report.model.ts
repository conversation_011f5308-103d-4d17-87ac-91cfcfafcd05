import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {Action} from './action.model';
import {LocationFive} from './location-five.model';
import {LocationFour} from './location-four.model';
import {LocationOne} from './location-one.model';
import {LocationSix} from './location-six.model';
import {LocationThree} from './location-three.model';
import {LocationTwo} from './location-two.model';
import {PermitRiskControl} from './permit-risk-control.model';
import {PermitRoleStatus} from './permit-role-status.model';
import {PermitStatus} from './permit-status.model';
import {RiskAssessment} from './risk-assessment.model';
import {User} from './user.model';

@model()
export class PermitReport extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  supportingDocuments?: string[];

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  permitStartDate?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  permitEndDate?: string;

  @property({
    type: 'string',
  })
  workDescription?: string;

  @property({
    type: 'string',
  })
  nameOfSiteSupervisor?: string;

  @property({
    type: 'string',
  })
  applicantContactNo?: string;

  @property({
    type: 'string',
  })
  supervisorContactNo?: string;

  @property({
    type: 'number',
  })
  noOfWorkers?: number;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  workOrderNo?: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  permitType?: string[];

  @property({
    type: 'string',
  })
  permitTag?: string;

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  permitWorkType?: 'High-Risk Hazard' | 'Routine';

  @property({
    type: 'string',
  })
  status?: 'Pending Review' | 'Pending Assessment' | 'Pending Approval' | 'Pending Work Commencement' | 'Active' | 'Timed Out' | 'Withdrawn' | 'Closed' | 'Suspended' | 'Returned' | 'Suspended & Closed' | 'Acknowledged & Closed';

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(PermitRiskControl)

  })
  permitRiskControl?: PermitRiskControl[];

  @property({
    type: 'object',
    itemType: PermitRoleStatus,
    jsonSchema: getJsonSchema(PermitRoleStatus)
  })
  applicantStatus?: PermitRoleStatus;

  @property({
    type: 'object',
    itemType: PermitRoleStatus,
    jsonSchema: getJsonSchema(PermitRoleStatus)
  })
  reviewerStatus?: PermitRoleStatus;

  @property({
    type: 'object',
    itemType: PermitRoleStatus,
    jsonSchema: getJsonSchema(PermitRoleStatus)
  })
  assessorStatus?: PermitRoleStatus;

  @property({
    type: 'object',
    itemType: PermitRoleStatus,
    jsonSchema: getJsonSchema(PermitRoleStatus)
  })
  approverStatus?: PermitRoleStatus;

  @property({
    type: 'object',
    itemType: PermitStatus,
    jsonSchema: getJsonSchema(PermitStatus)
  })
  permitStatus?: PermitStatus;

  @property({
    type: 'object',
    itemType: PermitStatus,
    jsonSchema: getJsonSchema(PermitStatus)
  })
  closeoutStatus?: PermitStatus;

  @property({
    type: 'object',
    itemType: PermitStatus,
    jsonSchema: getJsonSchema(PermitStatus)
  })
  acknowledgementStatus?: PermitStatus;

  @belongsTo(() => User)
  applicantId: string;

  @belongsTo(() => User)
  assessorId: string;

  @belongsTo(() => User)
  approverId: string;

  @belongsTo(() => LocationFive)
  locationFiveId: string;

  @belongsTo(() => LocationFour)
  locationFourId: string;

  @belongsTo(() => LocationOne)
  locationOneId: string;

  @belongsTo(() => LocationSix)
  locationSixId: string;

  @belongsTo(() => LocationThree)
  locationThreeId: string;

  @belongsTo(() => LocationTwo)
  locationTwoId: string;

  @hasMany(() => Action, {keyTo: 'objectId'})
  permitReportActions: Action[];

  @belongsTo(() => User)
  reviewerId: string;

  applicant: User;
  assessor: User;
  approver: User;
  acknowledger: User;

  locationFive: LocationFive;
  locationFour: LocationFour;
  locationOne: LocationOne;
  locationSix: LocationSix;
  locationThree: LocationThree;
  locationTwo: LocationTwo;
  reviewer: User;
  riskAssessment: RiskAssessment;
  @belongsTo(() => User)
  acknowledgerId: string;

  @belongsTo(() => RiskAssessment)
  riskAssessmentId: string;

  constructor(data?: Partial<PermitReport>) {
    super(data);
  }
}

export interface PermitReportRelations {
  // describe navigational properties here
}

export type PermitReportWithRelations = PermitReport & PermitReportRelations;
