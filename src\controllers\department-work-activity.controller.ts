import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Department,
  WorkActivity,
} from '../models';
import {DepartmentRepository} from '../repositories';

export class DepartmentWorkActivityController {
  constructor(
    @repository(DepartmentRepository) protected departmentRepository: DepartmentRepository,
  ) { }

  @get('/departments/{id}/work-activities', {
    responses: {
      '200': {
        description: 'Array of Department has many WorkActivity',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(WorkActivity)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<WorkActivity>,
  ): Promise<WorkActivity[]> {
    return this.departmentRepository.workActivities(id).find(filter);
  }

  @post('/departments/{id}/work-activities', {
    responses: {
      '200': {
        description: 'Department model instance',
        content: {'application/json': {schema: getModelSchemaRef(WorkActivity)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Department.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkActivity, {
            title: 'NewWorkActivityInDepartment',
            exclude: ['id'],
            optional: ['departmentId']
          }),
        },
      },
    }) workActivity: Omit<WorkActivity, 'id'>,
  ): Promise<WorkActivity> {
    return this.departmentRepository.workActivities(id).create(workActivity);
  }

  @patch('/departments/{id}/work-activities', {
    responses: {
      '200': {
        description: 'Department.WorkActivity PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(WorkActivity, {partial: true}),
        },
      },
    })
    workActivity: Partial<WorkActivity>,
    @param.query.object('where', getWhereSchemaFor(WorkActivity)) where?: Where<WorkActivity>,
  ): Promise<Count> {
    return this.departmentRepository.workActivities(id).patch(workActivity, where);
  }

  @del('/departments/{id}/work-activities', {
    responses: {
      '200': {
        description: 'Department.WorkActivity DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(WorkActivity)) where?: Where<WorkActivity>,
  ): Promise<Count> {
    return this.departmentRepository.workActivities(id).delete(where);
  }
}
