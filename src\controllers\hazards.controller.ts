import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Hazards} from '../models';
import {HazardsRepository} from '../repositories';

export class HazardsController {
  constructor(
    @repository(HazardsRepository)
    public hazardsRepository : HazardsRepository,
  ) {}

  @post('/hazards')
  @response(200, {
    description: 'Hazards model instance',
    content: {'application/json': {schema: getModelSchemaRef(Hazards)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Hazards, {
            title: 'NewHazards',
            exclude: ['id'],
          }),
        },
      },
    })
    hazards: Omit<Hazards, 'id'>,
  ): Promise<Hazards> {
    return this.hazardsRepository.create(hazards);
  }

  @get('/hazards/count')
  @response(200, {
    description: 'Hazards model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Hazards) where?: Where<Hazards>,
  ): Promise<Count> {
    return this.hazardsRepository.count(where);
  }

  @get('/hazards')
  @response(200, {
    description: 'Array of Hazards model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Hazards, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Hazards) filter?: Filter<Hazards>,
  ): Promise<Hazards[]> {
    return this.hazardsRepository.find(filter);
  }

  @patch('/hazards')
  @response(200, {
    description: 'Hazards PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Hazards, {partial: true}),
        },
      },
    })
    hazards: Hazards,
    @param.where(Hazards) where?: Where<Hazards>,
  ): Promise<Count> {
    return this.hazardsRepository.updateAll(hazards, where);
  }

  @get('/hazards/{id}')
  @response(200, {
    description: 'Hazards model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Hazards, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Hazards, {exclude: 'where'}) filter?: FilterExcludingWhere<Hazards>
  ): Promise<Hazards> {
    return this.hazardsRepository.findById(id, filter);
  }

  @patch('/hazards/{id}')
  @response(204, {
    description: 'Hazards PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Hazards, {partial: true}),
        },
      },
    })
    hazards: Hazards,
  ): Promise<void> {
    await this.hazardsRepository.updateById(id, hazards);
  }

  @put('/hazards/{id}')
  @response(204, {
    description: 'Hazards PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() hazards: Hazards,
  ): Promise<void> {
    await this.hazardsRepository.replaceById(id, hazards);
  }

  @del('/hazards/{id}')
  @response(204, {
    description: 'Hazards DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.hazardsRepository.deleteById(id);
  }
}
