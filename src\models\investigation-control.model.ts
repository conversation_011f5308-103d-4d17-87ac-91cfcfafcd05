import {Entity, model, property} from '@loopback/repository';
import {getJsonSchema} from '@loopback/rest';
import {InvestigationIdentifiedPreventiveControl} from './investigation-identified-preventive-control.model';

@model()
export class InvestigationControl extends Entity {

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(InvestigationIdentifiedPreventiveControl)

  })
  identifiedPreventiveControls?: InvestigationIdentifiedPreventiveControl[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(InvestigationIdentifiedPreventiveControl)

  })
  unidentifiedPreventiveControls?: InvestigationIdentifiedPreventiveControl[];

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(InvestigationIdentifiedPreventiveControl)

  })
  identifiedMitigativeControls?: InvestigationIdentifiedPreventiveControl[];


  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: getJsonSchema(InvestigationIdentifiedPreventiveControl)

  })
  unidentifiedMitigativeControls?: InvestigationIdentifiedPreventiveControl[];

  constructor(data?: Partial<InvestigationControl>) {
    super(data);
  }
}

export interface InvestigationControlRelations {
  // describe navigational properties here
}

export type InvestigationControlWithRelations = InvestigationControl & InvestigationControlRelations;
