import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, juggler, repository} from '@loopback/repository';
import {Ott, OttTask, OttTaskRelations, User} from '../models';
import {OttRepository} from './ott.repository';
import {UserRepository} from './user.repository';

export class OttTaskRepository extends DefaultCrudRepository<
  OttTask,
  typeof OttTask.prototype.id,
  OttTaskRelations
> {

  public readonly ott: BelongsToAccessor<Ott, typeof OttTask.prototype.id>;

  public readonly dependent: BelongsToAccessor<User, typeof OttTask.prototype.id>;

  constructor(
    @inject('datasources.config.dynamic') dataSource: juggler.DataSource, @repository.getter('OttRepository') protected ottRepositoryGetter: Getter<OttRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
  ) {
    super(OttTask, dataSource);
    this.dependent = this.createBelongsToAccessorFor('dependent', userRepositoryGetter,);
    this.registerInclusionResolver('dependent', this.dependent.inclusionResolver);
    this.ott = this.createBelongsToAccessorFor('ott', ottRepositoryGetter,);
    this.registerInclusionResolver('ott', this.ott.inclusionResolver);
    this.modelClass.observe('before save', async (ctx) => {
      const now = new Date().toISOString();
      if (ctx.instance) {
        if (ctx.isNewInstance) {
          ctx.instance.created = now;
        }
        ctx.instance.updated = now;
      } else {
        if (ctx.data) {
          ctx.data.updated = now;
        }
      }
    });
  }
}
