import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Dropdown,
  DropdownItems,
} from '../models';
import {DropdownRepository} from '../repositories';

export class DropdownDropdownItemsController {
  constructor(
    @repository(DropdownRepository) protected dropdownRepository: DropdownRepository,
  ) { }

  @get('/dropdowns/{id}/dropdown-items', {
    responses: {
      '200': {
        description: 'Array of Dropdown has many DropdownItems',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DropdownItems)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<DropdownItems>,
  ): Promise<DropdownItems[]> {
    return this.dropdownRepository.dropdownItems(id).find(filter);
  }

  @post('/dropdowns/{id}/dropdown-items', {
    responses: {
      '200': {
        description: 'Dropdown model instance',
        content: {'application/json': {schema: getModelSchemaRef(DropdownItems)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Dropdown.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropdownItems, {
            title: 'NewDropdownItemsInDropdown',
            exclude: ['id'],
            optional: ['dropdownId']
          }),
        },
      },
    }) dropdownItems: Omit<DropdownItems, 'id'>,
  ): Promise<DropdownItems> {
    return this.dropdownRepository.dropdownItems(id).create(dropdownItems);
  }

  @patch('/dropdowns/{id}/dropdown-items', {
    responses: {
      '200': {
        description: 'Dropdown.DropdownItems PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropdownItems, {partial: true}),
        },
      },
    })
    dropdownItems: Partial<DropdownItems>,
    @param.query.object('where', getWhereSchemaFor(DropdownItems)) where?: Where<DropdownItems>,
  ): Promise<Count> {
    return this.dropdownRepository.dropdownItems(id).patch(dropdownItems, where);
  }

  @del('/dropdowns/{id}/dropdown-items', {
    responses: {
      '200': {
        description: 'Dropdown.DropdownItems DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(DropdownItems)) where?: Where<DropdownItems>,
  ): Promise<Count> {
    return this.dropdownRepository.dropdownItems(id).delete(where);
  }
}
